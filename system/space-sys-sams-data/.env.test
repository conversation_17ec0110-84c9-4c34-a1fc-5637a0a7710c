# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
PANTHER_ERROR_SCREENSHOT_DIR=./var/error-screenshots
REDIS_URL='redis://localhost:6379'
SAMS_LOGIN=''
SAMS_PASSWORD=''
SAMS_CERT=''
DCP_SECRET=''
DCP_VECTOR=''
SAMS_URL_V2=''
SAMS_CONTRIB_URL_V2=''
SAMS_LOGIN_V2=''
SAMS_PASSWORD_V2=''
SAMS_CERT_V2=''
#