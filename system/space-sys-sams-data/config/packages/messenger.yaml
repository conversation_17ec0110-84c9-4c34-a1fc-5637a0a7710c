framework:
    messenger:
        # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
        # failure_transport: failed
        #failure_transport: failed
        transports:
            #failed: 'redis://redis_mym:6379/api_sys_corvet_messages?queue_name=failed'
            # https://symfony.com/doc/current/messenger.html#transport-configuration
            bo_cache_refresh: "enqueue://sqs?topic[name]=Cache-settings-integ&queue[name]=Cache-settings-integ&receiveTimeout=3"
            ws_asynchronous_calls:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                retry_strategy:
                    max_retries: 0
                    #delay: 1000
                    #multiplier: 1
        routing:
            # Route your messages to the transports
            App\Message\AsynchronousWSCallMessage: ws_asynchronous_calls
            App\Message\CacheNotification: bo_cache_refresh
