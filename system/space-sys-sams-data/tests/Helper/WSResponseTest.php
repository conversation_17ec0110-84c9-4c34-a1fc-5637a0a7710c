<?php

namespace App\Tests\Helper;
require __DIR__ . '/../../vendor/autoload.php';


use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class WSResponseTest extends TestCase
{
    public function testConstructorAndGetters(): void
    {
        $code = Response::HTTP_OK;
        $data = ['message' => 'Success'];
        $response = new WSResponse($code, $data);

        $this->assertEquals($code, $response->getCode());
        $this->assertEquals($data, $response->getData());
    }

    public function testSetters(): void
    {
        $newCode = Response::HTTP_CREATED;
        $newData = ['message' => 'Created'];

        $response = new WSResponse(Response::HTTP_OK, ['message' => 'Success']);
        $response->setCode($newCode);
        $response->setData($newData);

        $this->assertEquals($newCode, $response->getCode());
        $this->assertEquals($newData, $response->getData());
    }
}
