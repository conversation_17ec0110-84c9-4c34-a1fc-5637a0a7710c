<?php

namespace App\Tests\Helper;

require __DIR__ . '/../../vendor/autoload.php';

use App\Helper\SuccessResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class SuccessResponseTest extends TestCase
{
    public function testConstructorAndDefaultValues(): void
    {
        $data = ['message' => 'Operation successful'];
        $response = new SuccessResponse($data);

        $this->assertEquals(Response::HTTP_OK, $response->getCode()); 
        $this->assertEquals($data, $response->getData()); 
    }

    public function testSetCodeAndData(): void
    {
        $data = ['message' => 'Operation successful'];
        $response = new SuccessResponse($data);

        $response->setCode(Response::HTTP_CREATED);
        $response->setData(['message' => 'Resource created']);

        $this->assertEquals(Response::HTTP_CREATED, $response->getCode());
        $this->assertEquals(['message' => 'Resource created'], $response->getData());
    }

    public function testToArrayWithArrayData(): void
    {
        $data = ['message' => 'Operation successful'];
        $response = new SuccessResponse($data);

        $expected = [
            'content' => ['success' => $data],
            'code' => Response::HTTP_OK,
        ];

        $this->assertEquals($expected, $response->toArray());
    }

    public function testToArrayWithStringData(): void
    {
        $data = '{"message": "Operation successful"}'; // JSON string data
        $response = new SuccessResponse($data);

        $expected = [
            'content' => ['success' => ['message' => 'Operation successful']],
            'code' => Response::HTTP_OK,
        ];

        $this->assertEquals($expected, $response->toArray());
    }

    public function testToArrayWithNullData(): void
    {
        $response = new SuccessResponse(["data" => null]);
        $expected = [
            'content' => ['success' => ["data" => null]],
            'code' => Response::HTTP_OK,
        ];

        $this->assertEquals($expected, $response->toArray());
    }
}
