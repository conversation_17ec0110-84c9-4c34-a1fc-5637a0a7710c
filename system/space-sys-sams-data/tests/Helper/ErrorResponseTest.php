<?php

namespace Tests\Helper;

require __DIR__ . '/../../vendor/autoload.php';

use App\Helper\ErrorResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class ErrorResponseTest extends TestCase
{
    public function testToArrayWithStringError()
    {
        $errorResponse = new ErrorResponse('An error occurred');

        $result = $errorResponse->toArray();

        $this->assertSame(Response::HTTP_BAD_REQUEST, $result['code']);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertSame('An error occurred', $result['content']['error']['message']);
        $this->assertArrayHasKey('errors', $result['content']['error']);
        $this->assertSame('An error occurred', $result['content']['error']['errors']);
    }

    public function testToArrayWithArrayError()
    {
        $errors = ['field' => 'Invalid value'];
        $errorResponse = new ErrorResponse($errors, Response::HTTP_UNPROCESSABLE_ENTITY);

        $result = $errorResponse->toArray();

        $this->assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $result['code']);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertSame('error occured ' . json_encode($errors), $result['content']['error']['message']);
        $this->assertArrayHasKey('errors', $result['content']['error']);
        $this->assertSame($errors, $result['content']['error']['errors']);
    }

    public function testSetters()
    {
        $errorResponse = new ErrorResponse('Initial error');

        $errorResponse->setCode(Response::HTTP_INTERNAL_SERVER_ERROR)
            ->setMessage('Updated error message')
            ->setErrors(['key' => 'value']);

        $result = $errorResponse->toArray();

        $this->assertSame(Response::HTTP_INTERNAL_SERVER_ERROR, $result['code']);
        $this->assertArrayHasKey('error', $result['content']);
        $this->assertArrayHasKey('message', $result['content']['error']);
        $this->assertSame('Updated error message', $result['content']['error']['message']);
        $this->assertArrayHasKey('errors', $result['content']['error']);
        $this->assertSame(['key' => 'value'], $result['content']['error']['errors']);
    }
}
