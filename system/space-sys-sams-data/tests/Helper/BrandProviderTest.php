<?php

namespace App\Tests\Helper;
require __DIR__ . '/../../vendor/autoload.php';

use App\Helper\BrandProvider;
use PHPUnit\Framework\TestCase;

class BrandProviderTest extends TestCase
{
    public function testGetBrandIdWithValidBrand(): void
    {
        // Test for valid brand codes
        $this->assertEquals('83', BrandProvider::getBrandId('AL'), 'Failed to get correct ID for ALFA ROMEO');
        $this->assertEquals('31', BrandProvider::getBrandId('AP'), 'Failed to get correct ID for PEUGEOT');
        $this->assertEquals('70', BrandProvider::getBrandId('LA'), 'Failed to get correct ID for LANCIA');
    }

    public function testGetBrandIdWithEmptyIdBrand(): void
    {
        // Test for brand codes with empty IDs
        $this->assertNull(BrandProvider::getBrandId('VX'), 'VX should return null as it has no ID');
    }

    public function testGetBrandIdWithInvalidBrand(): void
    {
        // Test for invalid brand codes
        $this->assertNull(BrandProvider::getBrandId('INVALID'), 'Invalid brand code should return null');
        $this->assertNull(BrandProvider::getBrandId('XYZ'), 'Unknown brand code should return null');
    }

    public function testGetBrandIdWithCaseSensitivity(): void
    {
        // Test case sensitivity
        $this->assertNull(BrandProvider::getBrandId('al'), 'Lowercase brand codes should return null');
        $this->assertNull(BrandProvider::getBrandId('ap'), 'Lowercase brand codes should return null');
    }
}
