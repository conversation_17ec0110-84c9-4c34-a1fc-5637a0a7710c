<?php

namespace Tests\Helper;
require __DIR__ . '/../../vendor/autoload.php';


use App\Helper\UrlFormater;
use PHPUnit\Framework\TestCase;

class UrlFormaterTest extends TestCase
{
    public function testFormatUrl()
    {
        // Test case when URL does not have a trailing slash
        $url = "http://example.com";
        $formattedUrl = UrlFormater::formatUrl($url);
        $this->assertEquals("http://example.com", $formattedUrl);

      
        // Test case when URL is empty
        $url = "";
        $formattedUrl = UrlFormater::formatUrl($url);
        $this->assertEquals("", $formattedUrl);

        // Test case when URL contains query parameters and a trailing slash
        $url = "http://example.com/path?param=value/";
        $formattedUrl = UrlFormater::formatUrl($url);
        $this->assertEquals("http://example.com/path?param=value", $formattedUrl);
    }
}
