<?php

namespace App\Tests\Helper;

require __DIR__ . '/../../vendor/autoload.php';

use App\Helper\CultureParser;
use PHPUnit\Framework\TestCase;

class CultureParserTest extends TestCase
{
    public function testGetCountryAndLanguageWithUnderscore(): void
    {
        // Test valid culture strings with underscore
        $result = CultureParser::getCountryAndLanguage('en_US');
        $this->assertEquals(['country' => 'US', 'language' => 'en'], $result);

        $result = CultureParser::getCountryAndLanguage('fr_FR');
        $this->assertEquals(['country' => 'FR', 'language' => 'fr'], $result);
    }

    public function testGetCountryAndLanguageWithHyphen(): void
    {
        // Test valid culture strings with hyphen
        $result = CultureParser::getCountryAndLanguage('en-US');
        $this->assertEquals(['country' => 'US', 'language' => 'en'], $result);

        $result = CultureParser::getCountryAndLanguage('fr-FR');
        $this->assertEquals(['country' => 'FR', 'language' => 'fr'], $result);
    }

    public function testGetCountryAndLanguageWithInvalidFormat(): void
    {
        // Test invalid culture strings
        $result = CultureParser::getCountryAndLanguage('invalid');
        $this->assertEquals(['country' => '', 'language' => ''], $result);

        $result = CultureParser::getCountryAndLanguage('en');
        $this->assertEquals(['country' => '', 'language' => ''], $result);

    }

    public function testGetCountryAndLanguageWithEmptyString(): void
    {
        // Test empty culture string
        $result = CultureParser::getCountryAndLanguage('');
        $this->assertEquals(['country' => '', 'language' => ''], $result);
    }

    public function testGetCountryAndLanguageWithMixedCase(): void
    {
        // Test mixed case culture strings
        $result = CultureParser::getCountryAndLanguage('EN_us');
        $this->assertEquals(['country' => 'US', 'language' => 'en'], $result);

        $result = CultureParser::getCountryAndLanguage('Fr-fr');
        $this->assertEquals(['country' => 'FR', 'language' => 'fr'], $result);
    }

    public static function getCountryAndLanguage(string $culture): array
    {
        $country = $language = '';
    
        // Check for culture with underscore
        $data = explode('_', $culture);
        if (count($data) === 2 && !empty($data[0]) && !empty($data[1])) {
            $language = strtolower($data[0]);
            $country = strtoupper($data[1]);
        } else {
            // Check for culture with hyphen
            $data = explode('-', $culture);
            if (count($data) === 2 && !empty($data[0]) && !empty($data[1])) {
                $language = strtolower($data[0]);
                $country = strtoupper($data[1]);
            }
        }
    
        return [
            'country' => $country,
            'language' => $language,
        ];
    }
    

}
