<?php

namespace App\Tests\Helper;

require __DIR__ . '/../../vendor/autoload.php';


use App\Helper\FileHelper;
use PHPUnit\Framework\TestCase;

class FileHelperTest extends TestCase
{
    private FileHelper $fileHelper;

    protected function setUp(): void
    {
        $this->fileHelper = new FileHelper();
    }

    public function testSetExtensionWhenExtensionIsAlreadyPresent(): void
    {
        $fileName = 'certificate.cer';
        $extension = '.cer';

        $result = $this->fileHelper->setExtension($fileName, $extension);

        $this->assertEquals('certificate.cer', $result);
    }

    public function testSetExtensionWhenExtensionIsMissing(): void
    {
        $fileName = 'certificate';
        $extension = '.cer';

        $result = $this->fileHelper->setExtension($fileName, $extension);

        $this->assertEquals('certificate.cer', $result);
    }

    public function testSetExtensionWithDifferentExtension(): void
    {
        $fileName = 'document.txt';
        $extension = '.pdf';

        $result = $this->fileHelper->setExtension($fileName, $extension);

        $this->assertEquals('document.txt.pdf', $result);
    }

    public function testSetExtensionWithEmptyFileName(): void
    {
        $fileName = '';
        $extension = '.cer';

        $result = $this->fileHelper->setExtension($fileName, $extension);

        $this->assertEquals('.cer', $result);
    }

    public function testSetExtensionWithEmptyExtension(): void
    {
        $fileName = 'certificate';
        $extension = '';

        $result = $this->fileHelper->setExtension($fileName, $extension);

        $this->assertEquals('certificate', $result);
    }
}
