<?php

namespace App\Tests\Helper;
require __DIR__ . '/../../vendor/autoload.php';


use App\Helper\ResponseArrayFormat;
use App\Helper\ErrorResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class ResponseArrayFormatTest extends TestCase
{
    public function testToArrayImplementation(): void
    {
        $errors = 'Something went wrong';
        $errorResponse = new ErrorResponse($errors);

        $this->assertIsArray($errorResponse->toArray());

        $expected = [
            'code' => Response::HTTP_BAD_REQUEST,
            'content' => ['error' => ['message' => 'Something went wrong', 'errors' => 'Something went wrong']],
        ];

        $this->assertEquals($expected, $errorResponse->toArray());
    }
}
