<?php

namespace App\Tests\Helper;

require __DIR__ . '/../../vendor/autoload.php';


use App\Helper\LogMessageBuilder;
use App\Service\EncryptorService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class LogMessageBuilderTest extends TestCase
{
    private LogMessageBuilder $logMessageBuilder;
    private EncryptorService $encryptorService;

    protected function setUp(): void
    {
        $this->encryptorService = $this->createMock(EncryptorService::class);
        $this->logMessageBuilder = new LogMessageBuilder($this->encryptorService);
    }

    public function testBuildRequestTextLog(): void
    {
        $request = new Request([], [], [], [], [], [
            'REQUEST_URI' => '/api/test',
        ]);
        $request->headers->set('User-Agent', 'PHPUnit Test');
        $request->headers->set('account_id', '12345');
        
        $this->encryptorService
            ->expects($this->once())
            ->method('encrypt')
            ->with($this->stringContains('REQUEST  LOG'))
            ->willReturn('encrypted-log');

        $result = $this->logMessageBuilder->buildRequestTextLog($request);

        $this->assertStringContainsString('REQUEST', $result);
        $this->assertStringContainsString('encrypted-log', $result);
    }

    public function testBuildResponsetTextLog(): void
    {
        $request = new Request([], [], [], [], [], [
            'REQUEST_URI' => '/api/test',
        ]);
        $request->headers->set('User-Agent', 'PHPUnit Test');
        $request->headers->set('account_id', '12345');

        $response = new Response('Response content');

        $this->encryptorService
            ->expects($this->once())
            ->method('encrypt')
            ->with($this->stringContains('RESPONSE LOG'))
            ->willReturn('encrypted-log');

        $result = $this->logMessageBuilder->buildResponsetTextLog($request, $response);

        $this->assertStringContainsString('RESPONSE', $result);
        $this->assertStringContainsString('encrypted-log', $result);
    }

    public function testNonEncryptedPart(): void
    {
        $request = new Request([], [], [], [], [], [
            'REQUEST_URI' => '/api/test',
        ]);
        $request->headers->set('User-Agent', 'PHPUnit Test');
        $request->headers->set('account_id', '12345');

        $reflectionMethod = new \ReflectionMethod(LogMessageBuilder::class, '_getNonEncryptedPart');
        $reflectionMethod->setAccessible(true);

        $result = $reflectionMethod->invokeArgs($this->logMessageBuilder, [$request]);

        $this->assertStringContainsString('REQUEST', $result);
        $this->assertStringContainsString('PHPUnit Test', $result);
    }
}
