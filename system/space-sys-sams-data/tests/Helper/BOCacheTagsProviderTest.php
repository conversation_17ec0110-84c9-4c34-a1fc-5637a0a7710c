<?php

namespace App\Tests\Helper;

require __DIR__ . '/../../vendor/autoload.php';


use App\Helper\BOCacheTagsProvider;
use PHPUnit\Framework\TestCase;
    
class BOCacheTagsProviderTest extends TestCase
{
    public function testGetTagListWithNoParameters(): void
    {
        $tags = BOCacheTagsProvider::getTagList();
        
        // Assert that the result only contains the base tag
        $this->assertCount(1, $tags);
        $this->assertEquals(['BO_SETTINGS'], $tags);
    }

    public function testGetTagListWithBrand(): void
    {
        $tags = BOCacheTagsProvider::getTagList('brandName');
        
        // Assert the result contains the correct brand tag
        $this->assertCount(2, $tags);
        $this->assertEquals(['BO_SETTINGS', 'BO_SETTINGS_BRANDNAME'], $tags);
    }

    public function testGetTagListWithCountry(): void
    {
        $tags = BOCacheTagsProvider::getTagList(null, 'US');
        
        // Assert the result contains the correct country tag
        $this->assertCount(2, $tags);
        $this->assertEquals(['BO_SETTINGS', 'BO_SETTINGS_US'], $tags);
    }

    public function testGetTagListWithMultipleParameters(): void
    {
        // Test with brand, country, source, and language
        $tags = BOCacheTagsProvider::getTagList('brandName', 'US', 'web', 'en');
        
        // Assert the result contains the correct tags in the right order
        $this->assertCount(5, $tags);
        $this->assertEquals(
            ['BO_SETTINGS', 'BO_SETTINGS_BRANDNAME', 'BO_SETTINGS_BRANDNAME_US', 'BO_SETTINGS_BRANDNAME_US_WEB', 'BO_SETTINGS_BRANDNAME_US_WEB_EN'],
            $tags
        );
        
    }

    public function testGetTagListWithNullParameters(): void
    {
        // Test with null values for some parameters
        $tags = BOCacheTagsProvider::getTagList('brandName', null, 'web', null);
        
        // Assert the result contains the correct tags (only brand and source should appear)
        $this->assertCount(3, $tags);
        $this->assertEquals(
            ['BO_SETTINGS', 'BO_SETTINGS_BRANDNAME', 'BO_SETTINGS_BRANDNAME_WEB'],
            $tags
        );
        
    }

    public function testGetTagListWithUpperCaseValues(): void
    {
        // Test with uppercase values for parameters
        $tags = BOCacheTagsProvider::getTagList('brandName', 'us', 'web', 'en');
        
        // Assert the result contains the tags in uppercase
        $this->assertCount(5, $tags);
        $this->assertEquals(
            ['BO_SETTINGS', 'BO_SETTINGS_BRANDNAME', 'BO_SETTINGS_BRANDNAME_US', 'BO_SETTINGS_BRANDNAME_US_WEB', 'BO_SETTINGS_BRANDNAME_US_WEB_EN'],
            $tags
        );
        
    }
}
