<?php

namespace App\Tests\Controller;

require __DIR__ . '/../../vendor/autoload.php';

// use App\Controller\SubscriptionController;
// use App\Manager\SubscriptionManager;
// use App\Helper\ResponseArrayFormat;
// use PHPUnit\Framework\TestCase;
// use Symfony\Component\HttpFoundation\Request;
// use Symfony\Component\HttpFoundation\Response;
// use Symfony\Component\Validator\ConstraintViolationList;
// use Symfony\Component\Validator\Validator\ValidatorInterface;
// use Symfony\Component\DependencyInjection\ContainerInterface;
// use Symfony\Component\HttpFoundation\RequestStack;
// use Symfony\Component\Serializer\SerializerInterface;

// class SubscriptionControllerTest extends TestCase
// {
//     private $subscriptionManager;
//     private $validator;
//     private $controller;
//     private $requestStack;
//     private $container;
//     private $serializer;

//     protected function setUp(): void
//     {
//         $this->subscriptionManager = $this->createMock(SubscriptionManager::class);
//         $this->validator = $this->createMock(ValidatorInterface::class);
//         $this->requestStack = $this->createMock(RequestStack::class);
//         $this->serializer = $this->createMock(SerializerInterface::class);

//         // Create a mock container
//         $this->container = $this->createMock(ContainerInterface::class);

//         // Configure the container mock to handle multiple service retrievals
//         $this->container
//             ->method('has')
//             ->willReturn(true);

//         // Configure the container to return appropriate services
//         $this->container
//             ->method('get')
//             ->willReturnCallback(function($serviceName) {
//                 switch ($serviceName) {
//                     case 'request_stack':
//                         return $this->requestStack;
//                     case 'serializer':
//                         return $this->serializer;
//                     default:
//                         return null;
//                 }
//             });

//         // Create the controller with mocked dependencies
//         $this->controller = $this->getMockBuilder(SubscriptionController::class)
//             ->disableOriginalConstructor()
//             ->onlyMethods([]) // No methods to override
//             ->getMock();

//         $containerReflection = new \ReflectionProperty(get_class($this->controller), 'container');
//         $containerReflection->setAccessible(true);
//         $containerReflection->setValue($this->controller, $this->container);
//     }

//     /**
//      * Test successful subscription retrieval
//      */
//     public function testGetSubscriptionSuccess()
//     {
//         $userId = 'ACNT200000328091';
//         $vin = 'VR1URHNSSKW013718';
//         $target = 'B2C';

//         $request = new Request(
//             query: [
//                 'target' => $target
//             ],
//             server: [
//                 'HTTP_USERID' => $userId, 
//                 'HTTP_VIN' => $vin       
//             ]
//         );

//         $validationErrors = new ConstraintViolationList();
//         $this->validator
//             ->expects($this->once())
//             ->method('validate')
//             ->willReturn($validationErrors);

//         $mockResponseFormat = $this->createMock(ResponseArrayFormat::class);
//         $mockResponseFormat
//             ->method('toArray')
//             ->willReturn([
//                 'content' => [
//                     'success' => true, 
//                     'data' => [
//                         'subscriptionInfo' => [
//                             'id' => 'ACNT200000328091',
//                             'type' => 'premium'
//                         ]
//                     ]
//                 ],
//                 'code' => Response::HTTP_OK
//             ]);

//         $this->subscriptionManager
//             ->expects($this->once())
//             ->method('getSubscription')
//             ->with($userId, $vin, $target)
//             ->willReturn($mockResponseFormat);

//         $this->serializer
//             ->method('serialize')
//             ->willReturn(json_encode([
//                 'success' => true, 
//                 'data' => [
//                     'subscriptionInfo' => [
//                         'id' => 'ACNT200000328091',
//                         'type' => 'premium'
//                     ]
//                 ]
//             ]));

//         $response = $this->controller->getSubscription(
//             $request, 
//             $this->subscriptionManager, 
//             $this->validator
//         );

//         // Assertions
//         $this->assertInstanceOf(Response::class, $response);
//         $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
//         $this->assertJson($response->getContent());

//         $responseData = json_decode($response->getContent(), true);
//         $this->assertTrue($responseData['success']);
//         $this->assertArrayHasKey('data', $responseData);
//         $this->assertEquals('premium', $responseData['data']['subscriptionInfo']['type']);
//     }    
// }


use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;

use App\Controller\SubscriptionController;
use App\Manager\SubscriptionManager;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;

class SubscriptionControllerTest extends KernelTestCase
{
    private $subscriptionManager;
    private $validator;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();

        $this->subscriptionManager = $container->get(SubscriptionManager::class);
        $this->validator = $container->get(ValidatorInterface::class);
    }

    public function subscriptionOutput(): array
    {
        $data = [
            'vehicleProvisionings' => [
                "vehOrderId" => null,
                "vehicleProvisioningId" => "a1tVe000006MXQ5IAO",
                "vehicle" => "VR1URHNSSKW013718",
                "transactions" => [
                    "vin" => null,
                    "transactionVehicleOrderId" => null,
                    "transactionTermStartDate" => null,
                    "transactionStatusReason" => null,
                    "transactionStatus" => null,
                    "transactionServiceActivationDate" => null,
                    "transactionName" => null,
                    "transactionInitialTerm" => null,
                    "transactionId" => null,
                    "transactionEShopOrderId" => null,
                    "transactionContractEffectiveDate" => null,
                    "transactionChannel" => null,
                    "transactionCancelRequestDate" => null,
                    "transactionCancelReason" => null,
                    "transactionActivationStatus" => null,
                    "subscriptionCountry" => null,
                    "orderId" => null,
                    "hasSubscriptionProductAndCharge" => null,
                    "cultureCode" => null,
                    "billingAccountId" => null,
                    "assetId" => null,
                    "accountId" => null
                ],
                "subscription" => [
                    "subscriptionName" => "A-S00407030",
                    "subscriptionId" => "a0HVe000000qX7xMAE",
                    "status" => "Pending identification",
                    "rrdi" => null,
                    "restitutionId" => null,
                    "ratePlans" => [
                        [
                            "translatedDescription" => "Pack Navigation connectée",
                            "target" => "B2C",
                            "status" => "Active",
                            "ratePlanName" => "[TEST PURPOSES] NAVCO8/ZAR PSA - [TEST] 1 year paid ONE OFF",
                            "ratePlanId" => "ceb1bccf34c93491e6c376f9e6bfa5b2",
                            "ratePlanCharges" => [
                                [
                                    "type" => "One-Time",
                                    "ratePlanCurrency" => "EUR",
                                    "rateplanChargeTiers" => [
                                        [
                                            "tier" => "1",
                                            "ratePlanChargeCurrency" => "EUR",
                                            "price" => "109.00",
                                            "id" => "a0FVe0000024G3RMAU"
                                        ]
                                    ],
                                    "period" => null,
                                    "name" => "[TEST] 1 year paid",
                                    "model" => "Flat Fee Pricing",
                                    "listPrice" => "Per Billing Period",
                                    "id" => "a0DVe000001fK3pMAE",
                                    "bundleLevel" => null,
                                    "bundleGroup" => null
                                ]
                            ],
                            "productRatePlanId" => "8adceeb38e3b8d14018e3c50d91c3fb8",
                            "product" => [
                                "vehProvProductRelations" => [],
                                "type" => "Commercial Service",
                                "sku" => "SKU-00000088-DS",
                                "productStatus" => "Activated",
                                "productName" => "NAVCO8/ZAR PSA",
                                "productFeatures" => [
                                    [
                                        "featureName" => "Reconnaissance Vocale débarquée",
                                        "featureStatus" => null,
                                        "featureCode" => null,
                                        "servicesIds" => null,
                                        "type" => "FDS"
                                    ],
                                    [
                                        "featureName" => "Affichage bornes de recharge",
                                        "featureStatus" => null,
                                        "featureCode" => null,
                                        "servicesIds" => null,
                                        "type" => "FDS"
                                    ],
                                    [
                                        "featureName" => "Navigation connectée",
                                        "featureStatus" => null,
                                        "featureCode" => null,
                                        "servicesIds" => null,
                                        "type" => "FDS"
                                    ],
                                    [
                                        "featureName" => "Zone à risque en connexion",
                                        "featureStatus" => null,
                                        "featureCode" => null,
                                        "servicesIds" => null,
                                        "type" => "FDS"
                                    ]
                                ],
                                "productFamily" => "NAVCOZAR",
                                "productCommercialName" => "Pack Navigation connectée",
                                "productCode" => "8adcd9eb6f36eba7016f6b7a96001af0",
                                "marketingInfoProductId" => "8adcd9eb6f36eba7016f6b7a96001af0",
                                "groupName" => "NAVCOZAR",
                                "category" => "Base Products",
                                "brand" => "DS",
                                "activationChannel" => "PROMAN"
                            ],
                            "pricingModel" => "One Off",
                            "leads" => null,
                            "duration" => "12",
                            "country" => "FR",
                            "category" => "Subscribable",
                            "brand" => "DS"
                        ]
                    ],
                    "isRenewable" => false,
                    "isExtensible" => false,
                    "hasFreeTrial" => "False",
                    "cultureCode" => "fr-FR",
                    "clientReset" => false,
                    "b2bContractNumber" => null,
                    "b2bContractName" => null,
                    "b2bContractId" => null,
                    "autoRenew" => false,
                    "acntNum" => "A00520091"
                ],
                "subscriberAccount" => [
                    "target" => "PersonAccount",
                    "systemType" => "c@",
                    "street" => "99 boulevard de Prague",
                    "region" => null,
                    "postalCode" => "93160",
                    "legalEntityName" => null,
                    "externalId" => "DS-ACNT200000275706",
                    "customerPhoneNumber" => null,
                    "customerLastName" => "SAMS",
                    "customerLanguage" => "fr-FR",
                    "customerIdSams" => "0015E00003ChxkEQAR",
                    "customerFirstName" => "Hanumanth K",
                    "customerEmail" => "<EMAIL>",
                    "country" => "FR",
                    "civility" => "Mr.",
                    "city" => "Noisy-le-grand"
                ],
                "statusReason" => "ERROR",
                "startDate" => "2024-11-29T08:12:19.112Z",
                "provisionRequestId" => "674977572b623c6b608bb95d",
                "pmBundleId" => null,
                "pcsContractId" => null,
                "modelType" => "Standard",
                "lastModificationDate" => "2024-12-13T08:12:52.392Z",
                "invoiceOwner" => [
                    "target" => null,
                    "systemType" => null,
                    "street" => null,
                    "region" => null,
                    "postalCode" => null,
                    "legalEntityName" => null,
                    "externalId" => null,
                    "customerPhoneNumber" => null,
                    "customerLastName" => null,
                    "customerLanguage" => null,
                    "customerIdSams" => null,
                    "customerFirstName" => null,
                    "customerEmail" => null,
                    "country" => null,
                    "civility" => null,
                    "city" => null
                ],
                "endDate" => null,
                "deactivationDate" => null,
                "countryCode" => "FR",
                "channel" => "SAMS_Drupal",
                "brand" => "DS",
                "associationLevel" => "LOW",
                "associationJourney" => null,
                "associationId" => "92076a17-1192-4dee-9f11-9b000dba0a6e",
                "activationStatus" => "Pending activation",
                "activationDate" => null,
                "amRequestId" => "581731e9-4dd0-4abc-86c5-bb9b2cb9ff7d"
            ]
        ];
        return $data;
    }

    public function testGetSubscriptionSuccess()
    {
        $subscriptionManager = $this->createMock(SubscriptionManager::class);
        $subscriptionController = new SubscriptionController();
        $subscriptionController->setContainer(static::getContainer());

        $query = [
            'target' => 'B2C',
        ];
        $request = Request::create('/v1/subscription', 'GET', $query, [], [], []);
        $request->headers = new HeaderBag([
            'userId' => 'ACNT200000328091',
            'vin' => '12345678912345678'
        ]);
        $subscriptionManager->expects($this->once())->method('getSubscription')->willReturn(new SuccessResponse($this->subscriptionOutput(), 200));
        $response = $subscriptionController->getSubscription($request, $subscriptionManager, $this->validator);


        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());
    }

    public function testGetSubscriptionUnprocessableEntity()
    {
        $subscriptionManager = $this->createMock(SubscriptionManager::class);
        $subscriptionController = new SubscriptionController();
        $subscriptionController->setContainer(static::getContainer());

        $query = [
            'target' => 'B2C',
        ];
        $request = Request::create('/v1/subscription', 'GET', $query, [], [], []);
        $request->headers = new HeaderBag([
            'userId' => 'ACNT200000328091',
            'vin' => '123456789123456789'
        ]);
        $response = $subscriptionController->getSubscription($request, $subscriptionManager, $this->validator);


        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $this->assertJson($response->getContent());
    }
}
