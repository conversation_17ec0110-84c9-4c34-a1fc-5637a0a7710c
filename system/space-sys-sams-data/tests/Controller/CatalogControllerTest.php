<?php

namespace App\Tests\Controller;

require __DIR__ . '/../../vendor/autoload.php';

use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;

use App\Controller\CatalogController;
use App\Manager\CatalogManager;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;

class CatalogControllerTest extends KernelTestCase
{
    private $catalogManager;
    private $validator;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();

        $this->catalogManager = $container->get(CatalogManager::class);
        $this->validator = $container->get(ValidatorInterface::class);
    }

    public function catalogOutput(): array
    {
        $data = [
            "id" => "8adceeb38fbea01f018fc4bf967b0234",
            "SKU" => "SKU-00000353",
            "brand" => "FCA",
            "productEntityId" => null,
            "status" => "Activated",
            "name" => "Connect One FCA",
            "description" => "..",
            "type" => "Bundle",
            "rangeName" => null,
            "rangeLevel" => null,
            "rangeId" => null,
            "discountId" => null,
            "groupName" => "CONNECTONE",
            "groupMemberType" => "Base Products",
            "groupMemberLevel" => 1,
            "isGroupDefault" => 1,
            "ruleTag" => "AFTER_NCS_GENERIC,CONNECTONEFCA_NOT_SUBSCRIBED",
            "effectiveStartDate" => "2010-01-01T00:00:00.000Z",
            "effectiveEndDate" => "2100-01-01T00:00:00.000Z",
            "maxWarrantyBeginDate" => null,
            "minWarrantyBeginDate" => null,
            "familyName" => "CONNECTONE",
            "associationProcess" => "CVS",
            "superGroup" => "CONNECTPACK",
            "legalEntity" => "PSA Automobiles",
            "canalActivation" => "PROMAN",
            "error" => 0,
            "linkedFamilyName" => null,
            "code" => "ConnectOne_Code",
            "bundleGroup" => null,
            "bundleLevel" => null,
            "standaloneProducts" => [
                [
                    "id" => "8adca8879007e76801900c07e5a14833",
                    "SKU" => "SKU-000003451",
                    "brand" => "FCA",
                    "productEntityId" => null,
                    "status" => "Activated",
                    "name" => "My Assistant FCA",
                    "description" => "Basic service with emergency and breakdown call...",
                    "type" => "Commercial Service",
                    "rangeName" => null,
                    "rangeLevel" => null,
                    "rangeId" => null,
                    "discountId" => null,
                    "groupName" => "MYASSISTANT",
                    "groupMemberType" => "Base Products",
                    "groupMemberLevel" => 1,
                    "isGroupDefault" => 0,
                    "ruleTag" => "MyAssistant_NOT_SUBSCRIBED",
                    "effectiveStartDate" => "2010-01-01T00:00:00.000Z",
                    "effectiveEndDate" => "2100-12-31T00:00:00.000Z",
                    "maxWarrantyBeginDate" => null,
                    "minWarrantyBeginDate" => null,
                    "familyName" => "MYASSISTANT",
                    "associationProcess" => null,
                    "superGroup" => "MYASSISTANT",
                    "includedChannels" => null,
                    "excludedChannels" => null,
                    "legalEntity" => "PSA Automobiles",
                    "canalActivation" => "PROMAN",
                    "error" => 0,
                    "linkedFamilyName" => null,
                    "code" => "MyAssistant_Basic_Code",
                    "bundleGroup" => null,
                    "bundleLevel" => null,
                    "isMandatory" => 0,
                    "rpContextName" => "G10_FCABRANDS",
                    "functionalities" => [
                        [
                            "id" => "8adcb7bd900c3f3f01900cbdd75513ea",
                            "name" => "Vehicle Health Report",
                            "description" => "Monthly report about the status of the vehicle's key systems and components.",
                            "startValidityDate" => null,
                            "endValidityDate" => null,
                            "PSAFeatureCode" => "f_0003",
                            "provider" => null,
                            "creationDate" => "2024-06-18T07:16:18.000Z",
                            "modificationDate" => null,
                            "type" => "FDS",
                            "isMandatory" => 1,
                            "serviceIds" => ["SQDF", "VRC"]
                        ],
                        [
                            "id" => "8adcb7bd900c3f3f01900cbe12b413f0",
                            "name" => "In-Vehicle Notifications (Basic)",
                            "description" => "Delivers and organizes vehicle related notifications to the customer, such as: recalls, maintenance, subscription/promos, registration.",
                            "startValidityDate" => null,
                            "endValidityDate" => null,
                            "PSAFeatureCode" => "f_0053",
                            "provider" => null,
                            "creationDate" => "2024-06-18T07:16:18.000Z",
                            "modificationDate" => null,
                            "type" => "FDS",
                            "isMandatory" => 1,
                            "serviceIds" => ["IVM"]
                        ]
                    ],
                    "tnc" => [],
                    "services" => [],
                    "marketingProductSheetId" => null
                ]
            ],
            "functionalities" => [],
            "offers" => [
                [
                    "id" => "8adceeb38fbea01f018fc4bf9f82026d",
                    "country" => "GB",
                    "target" => "B2C",
                    "name" => "Connect One FCA - Initial Trial - 10y",
                    "status" => "Active",
                    "pricingModel" => "One Off",
                    "duration" => "118",
                    "channel" => null,
                    "effectiveStartDate" => "2019-01-01T00:00:00.000Z",
                    "effectiveEndDate" => "2100-01-01T00:00:00.000Z",
                    "promoCode" => null,
                    "creationDate" => "2024-07-26T07:05:46.000Z",
                    "modificationDate" => null,
                    "ruleTag" => null,
                    "default" => 0,
                    "rscTags" => " ",
                    "description" => "",
                    "priority" => 1,
                    "isFreeTrial" => 0,
                    "freeTrialDuration" => null,
                    "freeTrialDurationType" => null,
                    "category" => "Basic",
                    "isRenewable" => 0,
                    "mileage" => null,
                    "fromPrice" => null,
                    "maxNumberVin" => null,
                    "privacyFunctionality" => null,
                    "durationType" => "WarrantyStartDate",
                    "isBundleOffer" => 1,
                    "rpContextName" => "G10_FCABRANDS",
                    "thirdPartyMappingId" => null,
                    "IFRS15" => null,
                    "prices" => [
                        [
                            "id" => "8adcd55b8fbea02e018fc4c07dbd0a02+EUR",
                            "currency" => "EUR",
                            "name" => "Initial Trial",
                            "price" => 0,
                            "typeDiscount" => "FlatFee",
                            "discountPercentage" => null,
                            "discountAmount" => null,
                            "periodType" => null,
                            "costPercentage" => null,
                            "POBIdentifier" => "REV - Monthly recognition over time"
                        ]
                    ],
                    "ratePlanPrice" => 0,
                    "unDiscountedRatePlanPrice" => 0
                ]
            ],
            "tnc" => [],
            "bundles" => [],
            "services" => [],
            "marketingProductSheetId" => null,
            "associationLevel" => "LOW",
            "associationJourney" => null
        ];
        return $data;
    }

    public function testGetCatalogSuccess()
    {
        $catalogManager = $this->createMock(CatalogManager::class);
        $catalogController = new CatalogController();
        $catalogController->setContainer(static::getContainer());

        $query = [
            'brand' => 'DS',
            'country' => 'US',
            'language' => 'en'
        ];
        $request = Request::create('/v1/catalog', 'GET', $query, [], [], []);
        $request->headers = new HeaderBag([
            'userId' => 'ACNT200000328091',
            'vin' => '12345678912345678'
        ]);
        $catalogManager->expects($this->once())->method('getAssessedProducts')->willReturn(new SuccessResponse($this->catalogOutput(), 200));
        $response = $catalogController->getCatalog($request, $catalogManager, $this->validator);


        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());
    }

    public function testGetCatalogUnprocessableEntity()
    {
        $catalogManager = $this->createMock(CatalogManager::class);
        $catalogController = new CatalogController();
        $catalogController->setContainer(static::getContainer());

        $query = [
            'brand' => 'DSA',
            'country' => 'US',
            'language' => 'en'
        ];
        $request = Request::create('/v1/catalog', 'GET', $query, [], [], []);
        $request->headers = new HeaderBag([
            'userId' => 'ACNT200000328091',
            'vin' => '12345678912345678'
        ]);
        // $catalogManager->expects($this->once())->method('getAssessedProducts')->willReturn(new SuccessResponse($this->catalogOutput(), 200));
        $response = $catalogController->getCatalog($request, $catalogManager, $this->validator);


        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $this->assertJson($response->getContent());
    }
}