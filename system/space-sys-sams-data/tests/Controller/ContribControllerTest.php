<?php

namespace App\Tests\Controller;

require __DIR__ . '/../../vendor/autoload.php';


use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;

use App\Controller\ContribController;
use App\Manager\ContribManager;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;

class ContribControllerTest extends KernelTestCase
{
    private $contribManager;
    private $validator;

    protected function setUp(): void
    {
        self::bootKernel();
        $container = static::getContainer();

        $this->contribManager = $container->get(ContribManager::class);
        $this->validator = $container->get(ValidatorInterface::class);
    }

    public function contribOutput(): array
    {
        $data = [
            "homePage" => "https://ppr.services-store.dsautomobiles.co.uk/",
            "homePageSso" => "https://ppr.services-store.dsautomobiles.co.uk/login-redirect?redirect-url=https://ppr.services-store.dsautomobiles.co.uk/",
            "title" => "Telemaintenance",
            "fullDescription" => "<h4>LET DS&nbsp;LOOK AFTER YOUR SERVICE AND MAINTENANCE REMINDERS FOR YOU</h4>\n\n<p>You’ve got enough to think about, without having to remember when your vehicle needs servicing. &nbsp;</p>\n\n<p>With our intelligent Telemaintenance&nbsp;, DS&nbsp;continuously monitors the key functions of your vehicle and can let you know as soon as there is something that you need to get fixed.&nbsp;</p>\n\n<p>Telemaintenance&nbsp;is provided free of charge by DS&nbsp;for a minimum of 3 years, or for as long you own the vehicle and remain subscribed. &nbsp;Full information can be found in the<a data-entity-type=\"file\" data-entity-uuid=\"dabfd290-ed7f-4663-89f0-a217c1c45d62\" href=\"/sites/ds/files/uploaded-files/2102%20-%20DS%20Telemaintenance%20TsCs.pdf\" title=\" DS Telemaintenance TsCs.pdf \">&nbsp;</a><a data-entity-type=\"file\" data-entity-uuid=\"5ebfaca2-45dc-4503-a882-d0462a1233d9\" href=\"/sites/ds/files/uploaded-files/TC_Connect_One_UK.pdf\" title=\" DS Telemaintenance TsCs.pdf \"><u><strong>Telemaintenance&nbsp;Ts&amp;Cs</strong></u></a><a data-entity-type=\"file\" data-entity-uuid=\"dabfd290-ed7f-4663-89f0-a217c1c45d62\" href=\"/sites/ds/files/uploaded-files/2102%20-%20DS%20Telemaintenance%20TsCs.pdf\" title=\" DS Telemaintenance TsCs.pdf \">.&nbsp;</a></p>\n",
            "shortDescription" => "With Telemaintenance, DS lets you know if your vehicle requires scheduled maintenance or some extra attention.",
            "productUrl" => "https://ppr.services-store.dsautomobiles.co.uk/store/telemaintenance",
            "productUrlSso" => "https://ppr.services-store.dsautomobiles.co.uk/login-redirect?redirect-url=https://ppr.services-store.dsautomobiles.co.uk/store/telemaintenance",
            "productUrlCvs" => "https://ppr.services-store.dsautomobiles.co.uk/login-redirect?xcsrf=[VIN]&jwt=[TOKEN_CVS]&inboundApplication=[Mymark]&redirect-url=https://ppr.services-store.dsautomobiles.co.uk/store/telemaintenance",
            "marketingProductSheetId" => "35",
            "productId" => "8adc8f99680e4ebd016819a43e56517b",
            "features" => [
                [
                    "desc" => "<p>Real time maintenance alerts to look after your vehicle and to always know the next service needed</p>\r\n",
                    "icon" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/2024-05/TMTS%20%28big%29.png",
                    "full_desc" => "<p><strong>What you need to know, when you need to know</strong><br />\r\nDS intelligent maintenance features are your car’s personal assistant: the vehicle's key functions are constantly monitored and you are alerted if anything needs your attention.&nbsp;<br />\r\nWe also remind of your next service, making it easy to book an appointment in seconds.</p>\r\n"
                ]
            ],
            "topMainImage" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/styles/service_slider_element_desktop/public/2024-12/5-Telemaintenance-350x244.jpg",
            "tnc" => [
                "version" => "3.2",
                "title" => "T&C TM/TS",
                "id" => "36",
                "tncUrl" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/2024-05/TC_Connect_One_UK.pdf"
            ],
            "legals" => [],
            "consents" => [
                [
                    "category" => null,
                    "title" => "Geolocation Consents",
                    "shortDescription" => "I understand and accept that the provision of this(theses) service(s) requires vehicle geolocation and data will only be proceeded in accordance with the privacy policy.",
                    "longConsentDesc" => null,
                    "tnc" => [
                        "version" => "3.2",
                        "title" => "T&C TM/TS",
                        "id" => "36",
                        "tncUrl" => "https://ppr.services-store.dsautomobiles.co.uk/sites/ds/files/2024-05/TC_Connect_One_UK.pdf"
                    ],
                    "data" => null,
                    "purpose" => null,
                    "consumer" => null
                ]
            ]
        ];
        return $data;
    }

    public function testGetContribSuccess()
    {
        $catalogManager = $this->createMock(ContribManager::class);
        $contribController = new ContribController();
        $contribController->setContainer(static::getContainer());

        $query = [
            'brand' => 'DS',
            'culture' => 'en-US',
            'source' => 'APP'
        ];
        $request = Request::create('/v1/sams/contrib/getInfoByProduct/{productId}', 'GET', $query, [], [], []);
        $catalogManager->expects($this->once())->method('getContribDataByProduct')->willReturn(new SuccessResponse($this->contribOutput(), 200));
        $response = $contribController->getContribInfoByProduct('8adc8f99680e4ebd016819a43e56517b', $request, $catalogManager, $this->validator);


        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());
    }

    public function testGetContribUnprocessableEntity()
    {
        $catalogManager = $this->createMock(ContribManager::class);
        $contribController = new ContribController();
        $contribController->setContainer(static::getContainer());

        $query = [
            'brand' => 'DSA',
            'culture' => 'en-US',
            'source' => 'APP'
        ];
        $request = Request::create('/v1/sams/contrib/getInfoByProduct/{productId}', 'GET', $query, [], [], []);
        $response = $contribController->getContribInfoByProduct('8adc8f99680e4ebd016819a43e56517b', $request, $catalogManager, $this->validator);


        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $this->assertJson($response->getContent());
    }
}
