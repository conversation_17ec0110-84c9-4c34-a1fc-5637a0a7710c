<?php

namespace App\Tests\Trait;
require __DIR__ . '/../../vendor/autoload.php';

use App\Trait\LoggerTrait;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class LoggerTraitTest extends TestCase
{
    private LoggerInterface $mockLogger;

    protected function setUp(): void
    {
        $this->mockLogger = $this->createMock(LoggerInterface::class);
    }

    public function testSetLogger(): void
    {
        $classWithLoggerTrait = new class {
            use LoggerTrait;
        };

        $classWithLoggerTrait->setLogger($this->mockLogger);
        $this->assertTrue(true);
    }
}
