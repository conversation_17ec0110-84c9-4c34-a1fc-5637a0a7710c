<?php

namespace App\Tests\Trait;

require __DIR__ . '/../../vendor/autoload.php';

use App\Helper\ErrorResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;

class ValidationResponseTraitTest extends TestCase
{
        use \App\Trait\ValidationResponseTrait;

    public function testGetValidationMessagesWithNoErrors()
    {
        $errors = new ConstraintViolationList([]);
        
        $messages = self::getValidationMessages($errors);
        
        $this->assertEmpty($messages, 'Messages array should be empty when no errors exist');
    }

    public function testGetValidationMessagesWithSingleError()
    {
        $violation = new ConstraintViolation(
            'Invalid name',
            'Invalid name',
            [],
            'root',
            'name',
            'John'
        );
        
        $errors = new ConstraintViolationList([$violation]);
        
        $messages = self::getValidationMessages($errors);
        
        $this->assertCount(1, $messages, 'Messages array should contain one error');
        $this->assertArrayHasKey('name', $messages, 'Error message should be keyed by property name');
        $this->assertEquals('Invalid name', $messages['name'], 'Error message should match the violation message');
    }

    public function testGetValidationMessagesWithMultipleErrors()
    {
        $violations = [
            new ConstraintViolation(
                'Invalid name',
                'Invalid name',
                [],
                'root',
                'name',
                'John'
            ),
            new ConstraintViolation(
                'Invalid email',
                'Invalid email',
                [],
                'root',
                'email',
                'invalid@email'
            )
        ];
        
        $errors = new ConstraintViolationList($violations);
        
        $messages = self::getValidationMessages($errors);
        
        $this->assertCount(2, $messages, 'Messages array should contain two errors');
        $this->assertArrayHasKey('name', $messages, 'First error message should be keyed by property name');
        $this->assertArrayHasKey('email', $messages, 'Second error message should be keyed by property name');
        $this->assertEquals('Invalid name', $messages['name'], 'First error message should match the violation message');
        $this->assertEquals('Invalid email', $messages['email'], 'Second error message should match the violation message');
    }

    public function testGetValidationMessagesWithComplexPropertyPaths()
    {
        $violations = [
            new ConstraintViolation(
                'Invalid street',
                'Invalid street',
                [],
                'root',
                'address[0].street',
                'Invalid Street'
            )
        ];
        
        $errors = new ConstraintViolationList($violations);
        
        $messages = self::getValidationMessages($errors);
        
        $this->assertCount(1, $messages, 'Messages array should contain one error');
        $this->assertArrayHasKey('address0.street', $messages, 'Property path should be normalized');
        $this->assertEquals('Invalid street', $messages['address0.street'], 'Error message should match the violation message');
    }

    public function testGetValidationErrorResponse(): void
    {
        $messages = ['field1' => 'This is an error for field1', 'field2' => 'This is an error for field2'];
        $errorResponse = $this->getValidationErrorResponse($messages);

        $this->assertInstanceOf(ErrorResponse::class, $errorResponse);

        $responseArray = $errorResponse->toArray();
        
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $responseArray['code']);
        $this->assertEquals('validation_failed', $responseArray['content']['error']['message']); 
        $this->assertEquals($messages, $responseArray['content']['error']['errors']); 
    }
}