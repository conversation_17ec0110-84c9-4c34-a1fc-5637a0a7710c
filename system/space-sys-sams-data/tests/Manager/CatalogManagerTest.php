<?php

namespace App\Tests\Manager;

require __DIR__ . '/../../vendor/autoload.php';

use Symfony\Component\Validator\Validator\ValidatorInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

use App\Manager\CatalogManager;
use App\Service\CatalogService;
use App\Helper\ErrorResponse;
use PHPUnit\Framework\TestCase;
use App\Helper\BrandProvider;
use App\Helper\SuccessResponse;
use App\Helper\WSResponse;

class CatalogManagerTest extends TestCase
{
    private $catalogService;
    private $logger;
    private $catalogManager;
    private $brandProvider;
    private $validator;

    public function setUp(): void
    {
        parent::setUp();

        $this->catalogService = $this->createMock(CatalogService::class);
            
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->brandProvider = $this->createMock(BrandProvider::class);
        $this->brandProvider->method('getBrandId')->willReturn('FI');

        $this->catalogManager = new CatalogManager(
            $this->catalogService,
            [],
            $this->validator
        );
        $this->catalogManager->setLogger($this->logger);
    }

    public function catalogOutput(): string
    {
        $data = [
            "statusCode" => 200,
            "message" => "success",
            "data" => [
                [
                    "id" => "8adceeb38fbea01f018fc4bf967b0234",
                    "SKU" => "SKU-00000353",
                    "brand" => "FCA"
                ]
            ]
        ];
        return json_encode($data);
    }

    /**
     * Test error scenario
     */
    public function testGetAssessedProductsError()
    {
        // Prepare test data
        $params = [
            'brand' => 'DS',
            'vin' => 'VR1URHNSSKW013718',
            'language' => 'en',
            'country' => 'US',
            'userId' => 'ACNT200000328091',
        ];

        // Mock the service to throw an exception
        $this->catalogService
            ->expects($this->once())
            ->method('getAssessedProducts')
            ->willThrowException(new \Exception('Test error', 500));

        // Invoke the method
        $response = $this->catalogManager->getAssessedProducts($params);

        // Assertions for error response
        $this->assertInstanceOf(ErrorResponse::class, $response);
    }

    /**
     * Test success scenario
     */
    public function testGetAssessedProductsSuccess()
    {
        // Prepare test data
        $params = [
            'brand' => 'DS',
            'vin' => 'VR1URHNSSKW013718',
            'language' => 'en',
            'country' => 'US',
            'userId' => 'ACNT200000328091',
        ];

        // Mock the service to return a success response
        $this->catalogService
            ->expects($this->once())
            ->method('getAssessedProducts')
            ->willReturn(new WSResponse(200, $this->catalogOutput()));

        // Invoke the method
        $response = $this->catalogManager->getAssessedProducts($params);

        // Assertions for success response
        $this->assertInstanceOf(SuccessResponse::class, $response);
        $this->assertEquals(200, $response->getCode());
    }
}
