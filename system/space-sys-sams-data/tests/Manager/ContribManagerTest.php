<?php

namespace App\Tests\Manager;

require __DIR__ . '/../../vendor/autoload.php';

use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Exception;

use App\Helper\WSResponse;
use App\Manager\ContribManager;
use App\Service\ContribService;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Helper\ErrorResponse;


class ContribManagerTest extends TestCase
{
    private $contribService;
    private $validator;
    private $logger;
    private $contribManager;

    protected function setUp(): void
    {
        // Create mock objects
        $this->contribService = $this->createMock(ContribService::class);
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        
        $this->contribManager = new ContribManager(
            $this->contribService,
            ['GP', 'MQ']
        );

        $this->contribManager->setLogger($this->logger);
        
    }

    public function testGetContribDataByProductSuccess()
    {
        $brand = 'PEUGEOT';
        $culture = 'en-GB';
        $productId = '12345';
        $source = 'APP';
        $url = 'http://api.example.com/contrib';
        $data = "{'homePage':'https://ppr.services-store.dsautomobiles.co.uk/'}";


        // Configure contribService mock
        $this->contribService
            ->expects($this->once())
            ->method('getDefaultContribUrl')
            ->willReturn($url);

        $this->contribService
            ->expects($this->once())
            ->method('getContribDataByProduct')
            ->with($brand, $culture, $productId)
            ->willReturn(new WSResponse(200, $data));

        $result = $this->contribManager->getContribDataByProduct($brand, $culture, $productId, $source);

        $this->assertInstanceOf(SuccessResponse::class, $result);
    }

    public function testGetContribDataByProductWithNoUrlConfigured()
    {
        // Arrange
        $brand = 'PEUGEOT';
        $culture = 'en-GB';
        $productId = '12345';
        

        // Configure contribService mock
        $this->contribService
            ->method('getDefaultContribUrl')
            ->willReturn(null);

        // Configure logger mock
        $this->logger
            ->expects($this->once())
            ->method('info');

        $this->logger
            ->expects($this->once())
            ->method('error');

        // Act
        $result = $this->contribManager->getContribDataByProduct($brand, $culture, $productId);

        // Assert
        $this->assertInstanceOf(ErrorResponse::class, $result);
    }

    public function testGetContribDataByProductWithDOMTOM()
    {
        // Arrange
        $brand = 'PEUGEOT';
        $culture = 'fr-GP'; // Guadeloupe
        $productId = '8adc8f99680e4ebd016819a43e56517b';
        $url = 'http://api.example.com/contrib';
        $data = "{'homePage':'https://ppr.services-store.dsautomobiles.co.uk/'}";

        $this->contribService
            ->expects($this->once())
            ->method('getDefaultContribUrl')
            ->willReturn($url);

        // Configure contribService mock
        $this->contribService
            ->expects($this->once())
            ->method('getContribDataByProduct')
            ->with($brand, 'fr-FR', $productId) // Should use fr-FR for DOMTOM
            ->willReturn(new WSResponse(200, $data));

        // Act
        $result = $this->contribManager->getContribDataByProduct($brand, $culture, $productId);

        // Assert
        $this->assertInstanceOf(SuccessResponse::class, $result);
    }
}