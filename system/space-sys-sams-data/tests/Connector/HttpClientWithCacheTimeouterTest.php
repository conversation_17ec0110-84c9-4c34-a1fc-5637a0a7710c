<?php

namespace Tests\Connector;

require __DIR__ . '/../../vendor/autoload.php';


use App\Connector\HttpClientWithCacheTimeouter;
use App\Helper\WSResponse;
use App\Message\AsynchronousWSCallMessage;
use PHPUnit\Framework\TestCase;
use Psr\Cache\CacheItemInterface;
use Psr\Cache\CacheItemPoolInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Exception;

class HttpClientWithCacheTimeouterTest extends TestCase
{
    private $mockClient;
    private $mockCache;
    private $mockBus;
    private $mockLogger;
    private $httpClient;

    protected function setUp(): void
    {
        $this->mockClient = $this->createMock(HttpClientInterface::class);
        $this->mockCache = $this->createMock(CacheItemPoolInterface::class);
        $this->mockBus = $this->createMock(MessageBusInterface::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);

        $this->httpClient = new HttpClientWithCacheTimeouter(
            $this->mockClient,
            $this->mockCache,
            $this->mockBus
        );

        $reflection = new \ReflectionClass($this->httpClient);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->httpClient, $this->mockLogger);
    }

    public function testSuccessfulRequest()
    {
        $url = 'https://ppr-contextv2-2way.api.services-store.awsmpsa.com/catalog';
        $method = 'GET';
        $cacheKey = 'test_cache_key';
        $duration = 3600;
        $options = ['headers' => ['Accept' => 'application/json']];

        //create Mock successful HTTP response
        $mockResponse = $this->createMock(ResponseInterface::class);
        $mockResponse->method('getStatusCode')->willReturn(200);
        // $mockResponse->method('getData')->willReturn('{"key": "value"}');
        $mockResponse->method('getInfo')->willReturn(['total_time' => 0.5]);

        $this->mockClient->expects($this->once())
            ->method('request')
            ->with($method, $url, $options)
            ->willReturn($mockResponse);

        // Mock cache item
        $mockCacheItem = $this->createMock(CacheItemInterface::class);
        $this->mockCache->expects($this->once())
            ->method('deleteItem')
            ->with($cacheKey);
        
        $this->mockCache->expects($this->once())
            ->method('getItem')
            ->with($cacheKey)
            ->willReturn($mockCacheItem);

        $mockCacheItem->expects($this->once())
            ->method('set')
            ->with($this->isInstanceOf(WSResponse::class));
            
        $mockCacheItem->expects($this->once())
            ->method('expiresAfter')
            ->with($duration);

        $this->mockCache->expects($this->once())
            ->method('save')
            ->with($mockCacheItem);

        // Logger expectations
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with("Call URL: {$url}");

        $this->mockLogger->expects($this->once())
            ->method('debug')
            ->with($this->stringContains("**PERF** : $url"));

        // Make request
        $response = $this->httpClient->request($method, $url, $cacheKey, $duration, $options);

        // Assert response
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(200, $response->getCode());
    }

   
}