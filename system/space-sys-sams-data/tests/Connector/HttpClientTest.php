<?php

namespace Tests\Connector;
require __DIR__ . '/../../vendor/autoload.php';

use App\Connector\HttpClient;
use PHPUnit\Framework\TestCase;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Psr\Log\LoggerInterface;
use App\Helper\WSResponse;

class HttpClientTest extends TestCase
{
    private $mockLogger;
    private $mockClient;
    private $httpClient;

    protected function setUp(): void
    {
        $this->mockClient = $this->createMock(HttpClientInterface::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);
        
        $this->httpClient = new HttpClient($this->mockClient);

        $reflection = new \ReflectionClass($this->httpClient);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->httpClient, $this->mockLogger);
    }

    public function testRequestSuccess()
    {
        //Create Mock for successful response
        $mockResponse = $this->createMock(ResponseInterface::class);
        $mockResponse->method('getStatusCode')->willReturn(200);
        $mockResponse->method('getContent')->willReturn('{"key": "value"}');
        $mockResponse->method('getInfo')->willReturn(['total_time' => 0.5]);
        
        // Expect request method
        $this->mockClient->expects($this->once())
            ->method('request')
            ->with('GET', 'http://example.com', [])
            ->willReturn($mockResponse);

        // Expect logger methods 
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with('start call http://example.com');
        
        $this->mockLogger->expects($this->once())
            ->method('debug')
            ->with($this->stringContains('**PERF** : end call http://example.com'));

        // for the Request
        $response = $this->httpClient->request('GET', 'http://example.com', []);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(200, $response->getCode());
        $this->assertEquals('{"key": "value"}', $response->getData());
    }


    public function testRequestFailureWithErrorResponse()
    {
        // create Mock for error response
        $mockResponse = $this->createMock(ResponseInterface::class);
        $mockResponse->method('getStatusCode')->willReturn(500);
        $mockResponse->method('getContent')->willReturn('Internal Server Error');
        $mockResponse->method('getInfo')->willReturn(['total_time' => 0.5]);
        
        // Expect request method 
        $this->mockClient->expects($this->once())
            ->method('request')
            ->with('GET', 'https://ppr-contextv2-2way.api.services-store.awsmpsa.com/catalog', [])
            ->willReturn($mockResponse);

        // Expect logger methods 
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with('start call https://ppr-contextv2-2way.api.services-store.awsmpsa.com/catalog');
        
        $this->mockLogger->expects($this->once())
            ->method('debug')
            ->with($this->stringContains('**PERF** : end call https://ppr-contextv2-2way.api.services-store.awsmpsa.com/catalog'));

        // Perform the request
        $response = $this->httpClient->request('GET', 'https://ppr-contextv2-2way.api.services-store.awsmpsa.com/catalog', []);

        // Assertions
        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(500, $response->getCode());
        $this->assertEquals('Internal Server Error', $response->getData());
    }

   
}