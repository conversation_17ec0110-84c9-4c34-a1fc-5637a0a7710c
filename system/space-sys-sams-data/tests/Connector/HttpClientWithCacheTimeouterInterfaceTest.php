<?php

namespace Tests\Connector;
require __DIR__ . '/../../vendor/autoload.php';


use App\Connector\HttpClientWithCacheTimeouterInterface;
use App\Helper\WSResponse;
use PHPUnit\Framework\TestCase;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class HttpClientWithCacheTimeouterInterfaceTest extends TestCase
{
    /**
     * @var HttpClientWithCacheTimeouterInterface
     */
    private $implementation;

    protected function setUp(): void
    {
        // Create a mock for implementation of the interface
        $this->implementation = $this->createMock(HttpClientWithCacheTimeouterInterface::class);
    }

    public function testInterfaceMethodSignature()
    {
        // Test that the method exists with correct parameters
        $this->assertTrue(
            method_exists($this->implementation, 'request'),
            'Implementation must have request method'
        );

        // Get method parameters using reflection
        $reflection = new \ReflectionClass(HttpClientWithCacheTimeouterInterface::class);
        $method = $reflection->getMethod('request');
        $parameters = $method->getParameters();

        $this->assertEquals(5, count($parameters), 'request method must have 5 parameters');

        $this->assertEquals('method', $parameters[0]->getName());
        $this->assertEquals('string', $parameters[0]->getType()->getName());
        
        $this->assertEquals('url', $parameters[1]->getName());
        $this->assertEquals('string', $parameters[1]->getType()->getName());
        
        $this->assertEquals('cacheKey', $parameters[2]->getName());
        $this->assertEquals('string', $parameters[2]->getType()->getName());
        
        $this->assertEquals('duration', $parameters[3]->getName());
        $this->assertEquals('int', $parameters[3]->getType()->getName());
        
        $this->assertEquals('options', $parameters[4]->getName());
        $this->assertEquals('array', $parameters[4]->getType()->getName());
        $this->assertTrue($parameters[4]->isOptional(), 'options parameter must be optional');

        $returnType = $method->getReturnType();
        $this->assertEquals(WSResponse::class, $returnType->getName());
    }

    public function testRequestReturnsWSResponse()
    {
        $expectedResponse = $this->createMock(WSResponse::class);
        
        $this->implementation
            ->method('request')
            ->willReturn($expectedResponse);

        $response = $this->implementation->request(
            'GET',
            'https://ppr-contextv2-2way.api.services-store.awsmpsa.com/catalog',
            'test_cache_key',
            3600,
            []
        );

        $this->assertInstanceOf(
            WSResponse::class,
            $response,
            'request method must return instance of WSResponse'
        );
    }

    public function testRequestThrowsTransportException()
    {
        $this->implementation
            ->method('request')
            ->willThrowException($this->createMock(TransportExceptionInterface::class));

        $this->expectException(TransportExceptionInterface::class);

        $this->implementation->request(
            'GET',
            'https://ppr-contextv2-2way.api.services-store.awsmpsa.com/catalog',
            'test_cache_key',
            3600,
            ['unsupported_option' => true]
        );
    }

    /**
     * @dataProvider validMethodProvider
     */
    public function testRequestAcceptsValidMethods(string $method)
    {
        $expectedResponse = $this->createMock(WSResponse::class);
        
        $this->implementation
            ->method('request')
            ->willReturn($expectedResponse);

        $response = $this->implementation->request(
            $method,
            'https://ppr-contextv2-2way.api.services-store.awsmpsa.com/catalog',
            'test_cache_key',
            3600
        );

        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function validMethodProvider(): array
    {
        return [
            ['GET'],
            ['POST'],
            ['PUT'],
            ['DELETE'],
            ['PATCH'],
            ['HEAD'],
            ['OPTIONS']
        ];
    }

    /**
     * @dataProvider invalidParameterProvider
     */
    public function testRequestWithInvalidParameters($method, $url, $cacheKey, $duration, $options)
    {
        $this->expectException(\TypeError::class);

        $this->implementation->request($method, $url, $cacheKey, $duration, $options);
    }

    public function invalidParameterProvider(): array
    {
        return [
            'invalid method type' => [null, 'http://example.com', 'cache_key', 3600, []],
            'invalid url type' => ['GET', null, 'cache_key', 3600, []],
            'invalid cache key type' => ['GET', 'http://example.com', null, 3600, []],
            'invalid duration type' => ['GET', 'http://example.com', 'cache_key', 'invalid', []],
            'invalid options type' => ['GET', 'http://example.com', 'cache_key', 3600, 'invalid'],
        ];
    }
}