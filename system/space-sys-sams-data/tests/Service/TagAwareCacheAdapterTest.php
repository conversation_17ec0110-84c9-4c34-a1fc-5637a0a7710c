<?php

namespace App\Tests\Service;

require __DIR__ . '/../../vendor/autoload.php';

use App\Service\TagAwareCacheAdapter;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Cache\Adapter\TagAwareAdapter;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

class TagAwareCacheAdapterTest extends TestCase
{
    private $cacheAdapter;
    private $mockCache;

    protected function setUp(): void
    {
        $filesystemAdapter = new FilesystemAdapter();
        $this->mockCache = new TagAwareAdapter($filesystemAdapter);
        
        // Instantiate the TagAwareCacheAdapter with the mocked cache
        $this->cacheAdapter = new TagAwareCacheAdapter($this->mockCache);
    }

    public function testInvalidateTags()
    {
        $tags = ['tag1', 'tag2'];

        $result = $this->cacheAdapter->invalidateTags($tags);

        // Assert that the result is true
        $this->assertTrue($result);
    }

    public function testGet()
    {
        $key = 'test_key';
        $data = 'test_data';

        $cacheItem = $this->mockCache->getItem($key);
        $cacheItem->set($data);
        $this->mockCache->save($cacheItem);

        // Define the callback to return 'test_data'
        $callback = function($item) use ($data) {
            $item->set($data);
            return $data;
        };

        $result = $this->cacheAdapter->get($key, $callback);

        $this->assertEquals($data, $result);
    }

    public function testDelete()
    {
        $key = 'test_key';

        // Set the item in the cache
        $cacheItem = $this->mockCache->getItem($key);
        $cacheItem->set('some_data');
        $this->mockCache->save($cacheItem);

        // Call the method and assert the result
        $result = $this->cacheAdapter->delete($key);

        $this->assertTrue($result);
    }

    public function testGetItem()
    {
        $key = 'test_key';
        $data = 'test_data';

        // Set the item in the cache
        $cacheItem = $this->mockCache->getItem($key);
        $cacheItem->set($data);
        $this->mockCache->save($cacheItem);

        // Call the method and assert the result
        $result = $this->cacheAdapter->getItem($key);

        $this->assertEquals($data, $result);
    }

    public function setItem($key, $data, array $tags = [], $ttl = null)
{
    $item = $this->cache->getItem($key);
    $item->set($data);
    $item->tag($tags);
    if ($ttl) {
        $item->expiresAfter($ttl);
    }
    $this->cache->save($item);
    var_dump($item); // Debugging line
    return $data;
}
}