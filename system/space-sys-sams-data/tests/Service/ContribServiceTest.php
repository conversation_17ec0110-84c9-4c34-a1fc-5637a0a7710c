<?php

namespace Tests\Service;

require __DIR__ . '/../../vendor/autoload.php';


use App\Service\ContribService;
use App\Helper\FileHelper;
use App\Helper\WSResponse;
use App\Connector\HttpClientWithCacheTimeouter;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;

class ContribServiceTest extends TestCase
{
    private $mockHttpClient;
    private $mockFileHelper;
    private $mockLogger;
    private $contribService;
    private $cacheDurations;
    private $timeouts;
    private $certName;
    private $authHttpLogin;
    private $authHttpPassword;
    private $certifDir;
    private $authHttpLoginV2;
    private $authHttpPasswordV2;
    private $contribUrlV2;

    protected function setUp(): void
    {
        $this->mockHttpClient = $this->createMock(HttpClientWithCacheTimeouter::class);
        $this->mockFileHelper = $this->createMock(FileHelper::class);
        $this->mockLogger = $this->createMock(LoggerInterface::class);

        // Set up test configuration values
        $this->cacheDurations = ['contrib' => 3600];
        $this->timeouts = ['contrib' => 30];
        $this->certName = 'test_cert';
        $this->authHttpLogin = 'testuser';
        $this->authHttpPassword = 'testpass';
        $this->certifDir = '/path/to/certs';
        $this->authHttpLoginV2 = 'testuser_v2';
        $this->authHttpPasswordV2 = 'testpass_v2';
        $this->contribUrlV2 = 'https://api-v2.example.com';

        $this->contribService = new ContribService(
            $this->mockHttpClient,
            $this->cacheDurations,
            $this->timeouts,
            $this->certName,
            $this->authHttpLogin,
            $this->authHttpPassword,
            $this->certifDir,
            $this->authHttpLoginV2,
            $this->authHttpPasswordV2,
            $this->contribUrlV2,
            $this->mockFileHelper
        );

        $this->contribService->setLogger($this->mockLogger);
    }

    public function testGetDefaultContribUrl()
    {
        $this->assertEquals($this->contribUrlV2, $this->contribService->getDefaultContribUrl());
    }

    public function testSetUrl()
    {
        $testUrl = 'https://test.example.com';
        $this->contribService->setUrl($testUrl);

        $reflection = new \ReflectionClass($this->contribService);
        $urlProperty = $reflection->getProperty('url');
        $urlProperty->setAccessible(true);
        
        $this->assertEquals($testUrl, $urlProperty->getValue($this->contribService));
    }

    /**
     * @dataProvider brandMappingProvider
     */
    public function testGetContribDataByProduct()
    {
        $testUrl = 'https://test.example.com';
        $this->contribService->setUrl($testUrl);

        $culture = 'en-US';
        $productId = 'PROD123';
        $source = 'APP';

        $this->mockFileHelper->expects($this->exactly(2))
            ->method('setExtension')
            ->willReturn(
                'test_cert.cer',
            );

        $expectedOptions = [
            'headers' => [
                'Content-Type' => 'application/json',
                'brand' => 'DS',
                'culture' => $culture
            ],
            'timeout' => $this->timeouts['contrib'],
            'local_cert' => $this->certifDir . '/test_cert.cer',
            'local_pk' => $this->certifDir . '/test_cert.cer',
            'auth_basic' => [$this->authHttpLogin, $this->authHttpPassword],
        ];

        $expectedCacheKey = "api_sys_sams_contrib_DS_{$culture}_{$productId}_last";

        $mockResponse = $this->createMock(WSResponse::class);
        
        $this->mockHttpClient->expects($this->once())
            ->method('request')
            ->with(
                Request::METHOD_GET,
                $testUrl . "/contrib/getInfoByProduct/{$productId}",
                $expectedCacheKey,
                $this->cacheDurations['contrib'],
                $expectedOptions
            )
            ->willReturn($mockResponse);

        $result = $this->contribService->getContribDataByProduct('DS', $culture, $productId, $source);

        $this->assertSame($mockResponse, $result);
    }

    public function brandMappingProvider(): array
    {
        return [
            'OP brand mapping' => ['OP', 'OV'],
            'VX brand mapping' => ['VX', 'OV'],
            'Other brand no mapping' => ['OTHER', 'OTHER'],
        ];
    }

    public function testGetContribDataWithMissingCacheDuration()
    {
        $testUrl = 'https://test.example.com';
        $this->contribService->setUrl($testUrl);

        // Create new service instance with empty cache durations
        $serviceWithEmptyCache = new ContribService(
            $this->mockHttpClient,
            [], // empty cache durations
            $this->timeouts,
            $this->certName,
            $this->authHttpLogin,
            $this->authHttpPassword,
            $this->certifDir,
            $this->authHttpLoginV2,
            $this->authHttpPasswordV2,
            $this->contribUrlV2,
            $this->mockFileHelper
        );

        $brand = 'TEST';
        $culture = 'en-US';
        $productId = 'PROD123';

        // Mock FileHelper behavior
        $this->mockFileHelper->expects($this->exactly(2))
            ->method('setExtension')
            ->willReturnOnConsecutiveCalls(
                'test_cert.cer',
                'test_cert.key'
            );

        // Mock successful response
        $mockResponse = $this->createMock(WSResponse::class);
        
        // Expect HTTP client call with -1 cache duration
        $this->mockHttpClient->expects($this->once())
            ->method('request')
            ->with(
                Request::METHOD_GET,
                $this->anything(),
                $this->anything(),
                -1, // Default value when cache duration is not set
                $this->anything()
            )
            ->willReturn($mockResponse);

        $result = $serviceWithEmptyCache->getContribDataByProduct($brand, $culture, $productId);
        $this->assertSame($mockResponse, $result);
    }

    public function testGetContribDataWithEmptyContribUrlV2()
    {
        // Create service instance with empty contribUrlV2
        $serviceWithEmptyUrl = new ContribService(
            $this->mockHttpClient,
            $this->cacheDurations,
            $this->timeouts,
            $this->certName,
            $this->authHttpLogin,
            $this->authHttpPassword,
            $this->certifDir,
            $this->authHttpLoginV2,
            $this->authHttpPasswordV2,
            '', // empty contribUrlV2
            $this->mockFileHelper
        );

        $this->assertNull($serviceWithEmptyUrl->getDefaultContribUrl());
    }
}