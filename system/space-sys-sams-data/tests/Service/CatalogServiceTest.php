<?php

namespace App\Tests\Service;

require __DIR__ . '/../../vendor/autoload.php';


use App\Service\CatalogService;
use App\Helper\WSResponse;
use App\Helper\FileHelper;
use App\Helper\ResponseArrayFormat;
use App\Model\CatalogModel;
use App\Connector\HttpClientWithCacheTimeouter;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Component\Serializer\SerializerInterface;

class CatalogServiceTest extends TestCase
{
    private $httpClientMock;
    private $fileHelperMock;
    private $serializerMock;
    private $catalogService;

    private $vehicleInfo;

    protected function setUp(): void
    {
        // Create mocks for the dependencies
        $this->httpClientMock = $this->createMock(HttpClientWithCacheTimeouter::class);
        $this->fileHelperMock = $this->createMock(FileHelper::class);
        $this->serializerMock = $this->createMock(SerializerInterface::class);

        // Sample data for testing
        $this->vehicleInfo = [
            'store' => [
                'origin' => 'brand1',
                'country' => 'US'
            ],
            'vehicle' => '1234567890'
        ];

        // Create the service instance with mocked dependencies
        $this->catalogService = new CatalogService(
            $this->httpClientMock,
            ['product_assessed' => 3600],
            ['product_assessed' => 30],
            'authLogin',
            'authPassword',
            '/path/to/cert',
            'certNameV2',
            'authHttpLoginV2',
            'authHttpPasswordV2',
            'http://example.com',
            $this->fileHelperMock,
            $this->serializerMock
        );
    }

    public function testGetAssessedProducts(): void
    {
        // Mock the return value of the request method
        $wsResponseMock = $this->createMock(WSResponse::class);
        $this->httpClientMock->expects($this->once())
            ->method('request')
            ->with(
                Request::METHOD_POST,
                'http://example.com/catalog',
                $this->stringContains('api_sys_sams_catalog_1234567890_US_last'),
                3600,
                $this->anything()
            )
            ->willReturn($wsResponseMock);

        // Call the method under test
        $result = $this->catalogService->getAssessedProducts($this->vehicleInfo);

        // Assert that the result is a WSResponse
        $this->assertInstanceOf(WSResponse::class, $result);
    }
}
