<?php

namespace App\Tests\Service;

require __DIR__ . '/../../vendor/autoload.php';


use App\Service\CacheInfosProvider;
use PHPUnit\Framework\TestCase;

class CacheInfosProviderTest extends TestCase
{
    /**
     * Test for getCatalogCacheKeyForLastResponse method
     */
    public function testGetCatalogCacheKeyForLastResponse(): void
    {
        $brand = 'brand1';
        $culture = 'en';
        $vin = 'VR1URHNSSKW013718';

        // Expected output
        $expectedKey = 'api_sys_sams_catalog_VR1URHNSSKW013718_en_last';

        // Generate actual result using the method
        $actualKey = CacheInfosProvider::getCatalogCacheKeyForLastResponse($brand, $culture, $vin);

        // Assert the expected output equals the actual result
        $this->assertEquals($expectedKey, $actualKey);
    }

    /**
     * Test for getContribCacheKeyForLastResponse method
     */
    public function testGetContribCacheKeyForLastResponse(): void
    {
        // Test input
        $brand = 'brand1';
        $culture = 'en';
        $marketingProductSheetId = '987654321';

        // Expected output
        $expectedKey = 'api_sys_sams_contrib_brand1_en_987654321_last';

        // Generate actual result using the method
        $actualKey = CacheInfosProvider::getContribCacheKeyForLastResponse($brand, $culture, $marketingProductSheetId);

        // Assert the expected output equals the actual result
        $this->assertEquals($expectedKey, $actualKey);
    }

    /**
     * Test for getSubscriptionCacheKeyForLastResponse method
     */
    public function testGetSubscriptionCacheKeyForLastResponse(): void
    {
        // Test input
        $userId = 'user123';
        $vin = '**********';
        $target = 'target1';

        // Expected output
        $expectedKey = 'api_sys_sams_contrib_user123_**********_target1_last';

        // Generate actual result using the method
        $actualKey = CacheInfosProvider::getSubscriptionCacheKeyForLastResponse($userId, $vin, $target);

        // Assert the expected output equals the actual result
        $this->assertEquals($expectedKey, $actualKey);
    }
}
