<?php
namespace App\Tests\Service;

require __DIR__ . '/../../vendor/autoload.php';

use PHPUnit\Framework\TestCase;
use App\Service\SubscriptionService;
use App\Connector\HttpClientWithCacheTimeouter;
use App\Connector\HttpClient;
use App\Helper\FileHelper;
use App\Helper\WSResponse;
use Symfony\Component\HttpFoundation\Request;
use ReflectionMethod;
use Exception;
use Psr\Log\LoggerInterface;

class SubscriptionServiceTest extends TestCase
{
    private $subscriptionService;
    private $httpClientMock;
    private $httpClientSimpleMock;
    private $fileHelperMock;
    private $loggerMock;

    protected function setUp(): void
    {
        parent::setUp();

        // Create the mocks
        $this->httpClientMock = $this->createMock(HttpClientWithCacheTimeouter::class);
        $this->httpClientSimpleMock = $this->createMock(HttpClient::class);
        $this->fileHelperMock = $this->createMock(FileHelper::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        // Set up some parameters
        $cacheDurations = [
            'product_assessed' => 3600,
        ];

        $timeouts = [
            'product_assessed' => 30,
        ];

        $certNameV2 = 'cert_name';
        $certifDir = '/path/to/certificates';
        $authHttpLoginV2 = 'username';
        $authHttpPasswordV2 = 'password';
        $urlV2 = 'https://example.com';
        $cartUrlV2 = 'https://cart.example.com';

        // Instantiate the service with mocks
        $this->subscriptionService = new SubscriptionService(
            $this->httpClientMock,
            $this->httpClientSimpleMock,
            $cacheDurations,
            $timeouts,
            $certNameV2,
            $certifDir,
            $authHttpLoginV2,
            $authHttpPasswordV2,
            $urlV2,
            $cartUrlV2,
            $this->fileHelperMock
        );
    }

    public function testGetSubscriptionSuccess()
    {
        // Prepare the mock for FileHelper to return the cert file path
        $this->fileHelperMock->method('setExtension')
            ->willReturn('/path/to/certificates/cert_name.pem');
        
        // Prepare the mock for HttpClientWithCacheTimeouter to simulate a successful request
        $wsResponseMock = $this->createMock(WSResponse::class);
        $this->httpClientMock->expects($this->once())  // Expecting 'request' to be called once
            ->method('request')
            ->with(
                Request::METHOD_GET,
                'https://example.com/accounts/user123/vehicles/vin123/subscription?target=target1',
                $this->anything(), 
                3600,  // Cache duration
                $this->anything() 
            )
            ->willReturn($wsResponseMock);
    
        // Test parameters
        $userId = 'user123';
        $vin = 'vin123';
        $target = 'target1';
    
        // Call the method under test (getSubscription)
        $response = $this->subscriptionService->getSubscription($userId, $vin, $target);
    
        // Assert the response is a WSResponse instance
        $this->assertInstanceOf(WSResponse::class, $response);
    }
    

    public function testGetSubscriptionFailure()
    {
        // Prepare the mock for FileHelper to return the cert file path
        $this->fileHelperMock->method('setExtension')
            ->willReturn('/path/to/certificates/cert_name.pem');

        $this->httpClientMock->method('request')
            ->willThrowException(new Exception("Request failed"));

        // Test parameters
        $userId = 'user123';
        $vin = 'vin123';
        $target = 'target1';

        // Expect an exception to be thrown
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("Request failed");

        // Call the method under test, which should throw an exception
        $this->subscriptionService->getSubscription($userId, $vin, $target);
    }

    public function testGetCertifFile()
    {
        // Prepare the mock for FileHelper to return the cert file path
        $this->fileHelperMock->method('setExtension')
            ->willReturn('cert_name.pem'); // Return just the filename with extension

        $method = new ReflectionMethod(SubscriptionService::class, 'getCertifFile');
        $method->setAccessible(true);
        $certFile = $method->invoke($this->subscriptionService, 'cert_name', '.pem');

        // Assert that the method returns the correct file path
        $this->assertEquals('/path/to/certificates/cert_name.pem', $certFile);
    }

    public function testCheckoutItemSuccess()
    {
        // Prepare the mock for FileHelper to return the cert file paths
        $this->fileHelperMock->method('setExtension')
            ->willReturnMap([
                ['cert_name', '.cer', 'cert_name.cer'],
                ['cert_name', '.key', 'cert_name.key']
            ]);

        // Prepare the mock for HttpClient to simulate a successful request
        $wsResponseMock = $this->createMock(WSResponse::class);
        $this->httpClientSimpleMock->expects($this->once())
            ->method('request')
            ->with(
                Request::METHOD_POST,
                'https://cart.example.com/channel/cart/AR/IT/B2C/user123/items/checkout',
                $this->anything()
            )
            ->willReturn($wsResponseMock);

        // Test parameters
        $userId = 'user123';
        $brand = 'AR';
        $country = 'IT';
        $target = 'B2C';
        $serviceParams = [
            'vehicleDescription' => 'DS3 xxxxx',
            'items' => [
                [
                    'type' => 'zuoraRatePlan',
                    'id' => '8adc8f99697a22240169916559841311',
                    'tncVersion' => '1.4',
                    'tncId' => '162',
                    'tncUrl' => 'https://connect.opel.de/sites/ov/files/2024-08/DE_TC_Connect%20Plus_06_2024_7.pdf'
                ]
            ]
        ];

        // Call the method under test (checkoutItem)
        $response = $this->subscriptionService->checkoutItem($userId, $brand, $country, $target, $serviceParams);

        // Assert the response is a WSResponse instance
        $this->assertInstanceOf(WSResponse::class, $response);
    }

    public function testCheckoutItemFailure()
    {
        // Prepare the mock for FileHelper to return the cert file paths
        $this->fileHelperMock->method('setExtension')
            ->willReturnMap([
                ['cert_name', '.cer', 'cert_name.cer'],
                ['cert_name', '.key', 'cert_name.key']
            ]);

        $this->httpClientSimpleMock->method('request')
            ->willThrowException(new Exception("Checkout request failed"));

        // Test parameters
        $userId = 'user123';
        $brand = 'AR';
        $country = 'IT';
        $target = 'B2C';
        $serviceParams = ['test' => 'data'];

        // Expect an exception to be thrown
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("Checkout request failed");

        // Call the method under test, which should throw an exception
        $this->subscriptionService->checkoutItem($userId, $brand, $country, $target, $serviceParams);
    }
}
