<?php
namespace App\Tests\Service;

require __DIR__ . '/../../vendor/autoload.php';

use PHPUnit\Framework\TestCase;
use App\Service\SubscriptionService;
use App\Connector\HttpClientWithCacheTimeouter;
use App\Helper\FileHelper;
use App\Helper\WSResponse;
use Symfony\Component\HttpFoundation\Request;
use ReflectionMethod;
use Exception;

class SubscriptionServiceTest extends TestCase
{
    private $subscriptionService;
    private $httpClientMock;
    private $fileHelperMock;

    protected function setUp(): void
    {
        parent::setUp();

        // Create the mocks
        $this->httpClientMock = $this->createMock(HttpClientWithCacheTimeouter::class);
        $this->fileHelperMock = $this->createMock(FileHelper::class);

        // Set up some parameters
        $cacheDurations = [
            'product_assessed' => 3600,
        ];

        $timeouts = [
            'product_assessed' => 30,
        ];

        $certNameV2 = 'cert_name';
        $certifDir = '/path/to/certificates';
        $authHttpLoginV2 = 'username';
        $authHttpPasswordV2 = 'password';
        $urlV2 = 'https://example.com';

        // Instantiate the service with mocks
        $this->subscriptionService = new SubscriptionService(
            $this->httpClientMock,
            $cacheDurations,
            $timeouts,
            $certNameV2,
            $certifDir,
            $authHttpLoginV2,
            $authHttpPasswordV2,
            $urlV2,
            $this->fileHelperMock
        );
    }

    public function testGetSubscriptionSuccess()
    {
        // Prepare the mock for FileHelper to return the cert file path
        $this->fileHelperMock->method('setExtension')
            ->willReturn('/path/to/certificates/cert_name.pem');
        
        // Prepare the mock for HttpClientWithCacheTimeouter to simulate a successful request
        $wsResponseMock = $this->createMock(WSResponse::class);
        $this->httpClientMock->expects($this->once())  // Expecting 'request' to be called once
            ->method('request')
            ->with(
                Request::METHOD_GET,
                'https://example.com/accounts/user123/vehicles/vin123/subscription?target=target1',
                $this->anything(), 
                3600,  // Cache duration
                $this->anything() 
            )
            ->willReturn($wsResponseMock);
    
        // Test parameters
        $userId = 'user123';
        $vin = 'vin123';
        $target = 'target1';
    
        // Call the method under test (getSubscription)
        $response = $this->subscriptionService->getSubscription($userId, $vin, $target);
    
        // Assert the response is a WSResponse instance
        $this->assertInstanceOf(WSResponse::class, $response);
    }
    

    public function testGetSubscriptionFailure()
    {
        // Prepare the mock for FileHelper to return the cert file path
        $this->fileHelperMock->method('setExtension')
            ->willReturn('/path/to/certificates/cert_name.pem');

        $this->httpClientMock->method('request')
            ->willThrowException(new Exception("Request failed"));

        // Test parameters
        $userId = 'user123';
        $vin = 'vin123';
        $target = 'target1';

        // Expect an exception to be thrown
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("Request failed");

        // Call the method under test, which should throw an exception
        $this->subscriptionService->getSubscription($userId, $vin, $target);
    }

    public function testGetCertifFile()
    {
        // Prepare the mock for FileHelper to return the cert file path
        $this->fileHelperMock->method('setExtension')
            ->willReturn('cert_name.pem'); // Return just the filename with extension
    
        $method = new ReflectionMethod(SubscriptionService::class, 'getCertifFile');
        $method->setAccessible(true);
        $certFile = $method->invoke($this->subscriptionService, 'cert_name', '.pem');
    
        // Assert that the method returns the correct file path
        $this->assertEquals('/path/to/certificates/cert_name.pem', $certFile);
    }
}
