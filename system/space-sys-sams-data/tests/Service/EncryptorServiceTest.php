<?php

namespace App\Tests\Service;

require __DIR__ . '/../../vendor/autoload.php';

use App\Service\EncryptorService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class EncryptorServiceTest extends TestCase
{
    private $loggerMock;
    private $encryptorService;
    private $secret = 'my_secret_key';
    private $vector = 'my_secret_vector';

    protected function setUp(): void
    {
        // Create a mock for the LoggerInterface
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        // Instantiate the EncryptorService with the mock and test data
        $this->encryptorService = new EncryptorService($this->loggerMock, $this->secret, $this->vector);
    }

    public function testEncryptSuccess(): void
    {
        $data = 'my data to encrypt';

        // Mock openssl_encrypt to return a string (simulating successful encryption)
        $encryptedData = $this->encryptorService->encrypt($data);

        $this->assertNotEmpty($encryptedData);
        $this->assertNotEquals($data, $encryptedData);  // Encrypted data should not be the same as the input data
    }

  

    public function testDecryptSuccess(): void
    {
        $data = 'my data to encrypt';
        $encryptedData = $this->encryptorService->encrypt($data);

        $decryptedData = $this->encryptorService->decrypt($encryptedData);

        $this->assertEquals($data, $decryptedData);  // Decrypted data should match the original data
    }

    

    
}
