<?php

namespace App\Tests\Service;
require __DIR__ . '/../../vendor/autoload.php';

use App\Service\CacheAdapter;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Cache\Adapter\AdapterInterface;
use Symfony\Component\Cache\CacheItem;
use Symfony\Contracts\Cache\ItemInterface;

class CacheAdapterTest extends TestCase
{
    /**
     * @var CacheAdapter
     */
    private $cacheAdapter;

    /**
     * @var \PHPUnit\Framework\MockObject\MockObject|AdapterInterface
     */
    private $cacheMock;

    /**
     * Set up the test environment.
     */
    protected function setUp(): void
    {
        $this->cacheMock = $this->createMock(AdapterInterface::class);
        $this->cacheAdapter = new CacheAdapter($this->cacheMock);
    }

    public function testGetWhenItemIsNotCached(): void
    {
        // Create a new instance of CacheItem, which is the concrete class returned by getItem
        $cacheItem = new CacheItem();
        $cacheItem->set(null); // Initially set as null, simulating a cache miss
        
        // Mock the cache getItem method to return the CacheItem instance
        $this->cacheMock->method('getItem')
            ->willReturn($cacheItem);

        $callback = function () {
            return 'test_value';
        };

        // Mock the save method to ensure it's called
        $this->cacheMock->expects($this->once())
            ->method('save')
            ->with($cacheItem);

        $result = $this->cacheAdapter->get('test_key', $callback);
        $this->assertEquals('test_value', $result);
    }
  

    public function testDeleteWhenItemIsNotCached(): void
    {
        $cacheItem = new CacheItem();
        $cacheItem->set(null); // Simulate cache miss

        $this->cacheMock->method('getItem')
            ->willReturn($cacheItem);

        $result = $this->cacheAdapter->delete('test_key');

        // Assert that the result is true (no deletion needed)
        $this->assertTrue($result);
    }
}
