<?php

namespace App\Tests\Model;
require __DIR__ . '/../../vendor/autoload.php';

use App\Model\ProductStore;
use PHPUnit\Framework\TestCase;

class ProductStoreTest extends TestCase
{
    private $productStore;

    protected function setUp(): void
    {
        $this->productStore = new ProductStore();
    }

    public function testSetAndGetOrigin()
    {
        $origin = 'USA';
        $this->productStore->setOrigin($origin);
        $this->assertEquals($origin, $this->productStore->getOrigin());
    }

    public function testSetAndGetLocation()
    {
        $location = 'New York';
        $this->productStore->setLocation($location);
        $this->assertEquals($location, $this->productStore->getLocation());
    }

    public function testSetAndGetChannel()
    {
        $channel = 'Online';
        $this->productStore->setChannel($channel);
        $this->assertEquals($channel, $this->productStore->getChannel());
    }

    public function testSetAndGetAudience()
    {
        $audience = 'General Public';
        $this->productStore->setAudience($audience);
        $this->assertEquals($audience, $this->productStore->getAudience());
    }
}