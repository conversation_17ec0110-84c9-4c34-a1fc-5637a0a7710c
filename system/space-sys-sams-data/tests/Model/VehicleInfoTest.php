<?php

namespace App\Tests\Model;
require __DIR__ . '/../../vendor/autoload.php';

use App\Model\VehicleInfo;
use App\Model\ProductStore; // Ensure you include the ProductStore class
use PHPUnit\Framework\TestCase;

class VehicleInfoTest extends TestCase
{
    private $vehicleInfo;

    protected function setUp(): void
    {
        $this->vehicleInfo = new VehicleInfo();
    }

    public function testSetAndGetVehicle()
    {
        $vehicle = 'FCAHBTTG1R9007815';
        $this->vehicleInfo->setVehicle($vehicle);
        $this->assertEquals($vehicle, $this->vehicleInfo->getVehicle());
    }

    public function testSetAndGetCustomer()
    {
        $customer = 'John Doe';
        $this->vehicleInfo->setCustomer($customer);
        $this->assertEquals($customer, $this->vehicleInfo->getCustomer());
    }

    public function testSetAndGetCulture()
    {
        $culture = 'en-US';
        $this->vehicleInfo->setCulture($culture);
        $this->assertEquals($culture, $this->vehicleInfo->getCulture());
    }

    public function testSetAndGetBrand()
    {
        $brand = 'FIAT';
        $this->vehicleInfo->setBrand($brand);
        $this->assertEquals($brand, $this->vehicleInfo->getBrand());
    }

    public function testSetAndGetStore()
    {
        $store = new ProductStore();
        $store->setOrigin('USA'); // Example method, adjust as necessary
        $this->vehicleInfo->setStore($store);
        $this->assertEquals($store, $this->vehicleInfo->getStore());
    }
}