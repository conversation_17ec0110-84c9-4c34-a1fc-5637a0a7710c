<?php
namespace Tests\Validator;
require __DIR__ . '/../../vendor/autoload.php';


use App\Validator\BrandValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Choice;

class BrandValidatorTest extends TestCase
{
    /**
     * Test the `all()` method.
     */
    public function testAllReturnsCorrectBrands()
    {
        $expectedBrands = ['AL', 'AP', 'AC', 'DS', 'OP', 'VX', 'FT', 'FO', 'AH', 'AR', 'CY', 'DG', 'JE', 'LA', 'RM', 'MA'];
        
        $this->assertEquals($expectedBrands, BrandValidator::all());
    }

    /**
     * Test the `getConstraintsForAll()` method.
     */
    public function testGetConstraintsForAllReturnsCorrectConstraints()
    {
        $constraints = BrandValidator::getConstraintsForAll();

        // Assert that there are two constraints: NotBlank and Choice
        $this->assertCount(2, $constraints);

        $this->assertInstanceOf(NotBlank::class, $constraints[0]);
        $this->assertInstanceOf(Choice::class, $constraints[1]);
        $this->assertEquals(BrandValidator::all(), $constraints[1]->choices);
    }
}
