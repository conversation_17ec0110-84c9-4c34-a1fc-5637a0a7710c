<?php
namespace Tests\Validator;

require __DIR__ . '/../../vendor/autoload.php';

use App\Validator\LanguageValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Language;

class LanguageValidatorTest extends TestCase
{
    /**
     * Test valid language.
     */
    public function testValidLanguage()
    {
        $validator = Validation::createValidator();
        $validLanguage = 'en';

        // Validate the language
        $violations = $validator->validate($validLanguage, LanguageValidator::getConstraintsForLanguage());

        // Assert that there are no violations
        $this->assertCount(0, $violations);
    }

    /**
     * Test blank language.
     */
    public function testBlankLanguage()
    {
        $validator = Validation::createValidator();

        // Blank language code (empty string)
        $blankLanguage = '';

        // Validate the language
        $violations = $validator->validate($blankLanguage, LanguageValidator::getConstraintsForLanguage());
        $this->assertCount(1, $violations);
        $this->assertEquals('This value should not be blank.', $violations[0]->getMessage());
    }

    /**
     * Test null language.
     */
    public function testNullLanguage()
    {
        $validator = Validation::createValidator();

        // Null language value
        $nullLanguage = null;

        // Validate the language
        $violations = $validator->validate($nullLanguage, LanguageValidator::getConstraintsForLanguage());

        // Assert that there is one violation (NotBlank)
        $this->assertCount(1, $violations);
        $this->assertEquals('This value should not be blank.', $violations[0]->getMessage());
    }

}
