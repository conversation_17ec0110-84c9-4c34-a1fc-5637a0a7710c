<?php
namespace Tests\Validator;

require __DIR__ . '/../../vendor/autoload.php';

use App\Validator\Culture;
use App\Validator\CultureValidator;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Constraints\Country;
use Symfony\Component\Validator\Constraints\Language;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\ConstraintViolationListInterface;

class CultureValidatorTest extends TestCase
{
    private $validator;

    protected function setUp(): void
    {
        // Set up Symfony Validator
        $this->validator = Validation::createValidator();
    }

    public function testValidCulture()
    {
        $validCultures = ['en-US', 'fr-FR', 'de-DE', 'es-ES'];

        foreach ($validCultures as $culture) {
            $violations = $this->validator->validate($culture, new Culture());
            $this->assertCount(0, $violations, "Culture '$culture' should be valid.");
        }
    }

    public function testInvalidCultureLength()
    {
        $invalidCultures = ['en', 'enUS', 'english', 'en-XX-YY'];

        foreach ($invalidCultures as $culture) {
            $violations = $this->validator->validate($culture, new Culture());
            $this->assertCount(1, $violations, "Culture '$culture' should have length errors.");
            $this->assertEquals(
                "Invalid culture value (xx-XX)",
                $violations[0]->getMessage(),
                "Error message should be correct for culture '$culture'."
            );
        }
    }

   

    public function testInvalidLanguageCode()
    {
        $invalidCultures = ['xx-US', 'zz-GB', 'jj-DE'];

        foreach ($invalidCultures as $culture) {
            $violations = $this->validator->validate($culture, new Culture());
            $this->assertCount(1, $violations, "Culture '$culture' should be invalid.");
            $this->assertEquals(
                'Invalid Language value',
                $violations[0]->getMessage(),
                "Error message should be correct for language in culture '$culture'."
            );
        }
    }

}
