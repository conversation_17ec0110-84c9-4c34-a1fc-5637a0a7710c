<?php
namespace Tests\Validator;
require __DIR__ . '/../../vendor/autoload.php';
use App\Validator\CountryValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Country;

class CountryValidatorTest extends TestCase
{
    /**
     * Test the `getConstraintsForCountry()` method.
     */
    public function testGetConstraintsForCountryReturnsCorrectConstraints()
    {
        $constraints = CountryValidator::getConstraintsForCountry();

        // Assert that there are two constraints: NotBlank and Country
        $this->assertCount(2, $constraints);
        $this->assertInstanceOf(NotBlank::class, $constraints[0]);
        $this->assertInstanceOf(Country::class, $constraints[1]);
    }
}
