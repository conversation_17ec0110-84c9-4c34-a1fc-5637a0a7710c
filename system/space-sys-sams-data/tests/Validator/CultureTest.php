<?php

namespace App\Tests\Validator;
require __DIR__ . '/../../vendor/autoload.php';

use App\Validator\Culture;
use App\Validator\CultureValidator;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class CultureTest extends ConstraintValidatorTestCase
{
    protected function createValidator()
    {
        return new CultureValidator();
    }

    public function testValidCulture()
    {
        $constraint = new Culture();
        $this->validator->validate('en-US', $constraint);

        $this->assertNoViolation();
    }

}