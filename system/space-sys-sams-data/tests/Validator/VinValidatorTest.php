<?php
namespace Tests\Validator;

require __DIR__ . '/../../vendor/autoload.php';

use App\Validator\VinValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;
use Symfony\Component\Validator\Constraints\Length;

class VinValidatorTest extends TestCase
{
    /**
     * Test valid VIN.
     */
    public function testValidVin()
    {
        $validator = Validation::createValidator();

        // Valid VIN: 17 alphanumeric characters
        $validVin = '1HGCM82633A123456';
        $violations = $validator->validate($validVin, VinValidator::getConstraints());

        $this->assertCount(0, $violations);
    }

     /**
     * Test null VIN.
     */
    public function testNullVin()
    {
        $validator = Validation::createValidator();

        // Null VIN value
        $nullVin = null;

        // Validate the VIN
        $violations = $validator->validate($nullVin, VinValidator::getConstraints());

        // Assert that there is one violation (NotBlank)
        $this->assertCount(1, $violations);
        $this->assertEquals('This value should not be blank.', $violations[0]->getMessage());
    }
}