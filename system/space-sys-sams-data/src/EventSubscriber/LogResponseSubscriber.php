<?php

namespace App\EventSubscriber;

use App\Helper\LogMessageBuilder;
use App\Log\ApiLogger;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;


class LogResponseSubscriber implements EventSubscriberInterface
{
    const LOG_SUCCESS_COUNTRIES = ["JP", "KR"];

    /**
     * @var ApiLogger
     */
    private $_logger;

    /**
     * @var LogMessageBuilder
     */
    private $_logMessageBuilder;

    /**
     * @var LoggerInterface
     */
    private $_normalLogger;

    public function __construct(ApiLogger $logger, LogMessageBuilder $logMessageBuilder, LoggerInterface $normalLogger)
    {
        $this->_logger = $logger;
        $this->_logMessageBuilder = $logMessageBuilder;
        $this->_normalLogger = $normalLogger;
    }

    private function _isSupported(Request $request)
    {
        /**
         * only Exception /doc and /doc.json are autorized without  'mym-access-token' field in header 
         */
        return !in_array($request->getPathInfo(), ["/doc", "/doc.json", "/favicon.ico", "/"]);
    }

    private function _getLevel(Response $response)
    {
        $data = [];
        if ($response->getStatusCode() != Response::HTTP_NO_CONTENT ) {
            $content = $response->getContent();
            if ($content) {
                $data = json_decode($content, true);
            }
        }
        $level = "info";
        if (isset($data['errors']) || isset($data['warnings']) || isset($data['error']) || isset($data['warning']) || !$response->isSuccessful()) {
            $level = "error";
        }
        return $level;
    }

    public function logResponseData(ResponseEvent $responseEvent)
    {
        try {
            $request = $responseEvent->getRequest();
            $response = $responseEvent->getResponse();
            /**
             * only Exception /doc and /doc.json are autorized without  'mym-access-token' field in header 
             */
            if (!$this->_isSupported($request)) {
                return true;
            }
            $country = $this->_getCountry($responseEvent->getRequest()->headers->get('site-code', ''));
            $level = $this->_getLevel($response);
            
            if ($level == "error" || in_array($country, self::LOG_SUCCESS_COUNTRIES)) {
                $this->_logger->log('info', $this->_logMessageBuilder->buildRequestTextLog($request));
                $this->_logger->log($level, $this->_logMessageBuilder->buildResponsetTextLog($request, $response));
            }
        } catch (Exception $e) {
            $this->_normalLogger->error('LOG RESPONSE DATA ERROR ' . $e->getMessage());
        }
        return true;
    }

    private function _getCountry($siteCode = '')
    {
        $country = '';
        if ($siteCode) {
            $data = explode('_', $siteCode);
            if (count($data) === 3) {
                $country = strtoupper($data[1]);
            }
        }
        return $country;
    }
    /**
     * getSubscribedEvents
     */
    public static function getSubscribedEvents()
    {
        return [ 
            KernelEvents::RESPONSE => 'logResponseData',
        ];
    }
}
