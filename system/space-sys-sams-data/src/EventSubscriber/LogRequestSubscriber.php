<?php

namespace App\EventSubscriber;

use App\Helper\LogMessageBuilder;
use App\Log\ApiLogger;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;


class LogRequestSubscriber implements EventSubscriberInterface
{
    const QUERY_ID = 'query-id';

    /**
     * @var LoggerInterface
     */
    private $_normalLogger;

    public function __construct(LoggerInterface $normalLogger)
    {
        $this->_normalLogger = $normalLogger;
    }

    /**
     *
     * Method that generates a single token
     */
    private function _generateToken()
    {
        return md5(rand());
    }

    public function logRequestData(RequestEvent $requestEvent)
    {
        try {
            /**
             * only Exception /doc and /doc.json are autorized without  'mym-access-token' field in header 
             */
            if ($requestEvent->getRequest()->getPathInfo() == "/doc" || $requestEvent->getRequest()->getPathInfo() == "/doc.json") {
                return true;
            }
            $header = $requestEvent->getRequest()->headers->get(self::QUERY_ID);
            if (!$header) {
                $requestEvent->getRequest()->headers->set(self::QUERY_ID, $this->_generateToken());
            }
        } catch (Exception $e) {
            $this->_normalLogger->error('LOG REQUEST DATA ERROR ' . $e->getMessage());
        }
        return true;
    }


    /**
     * getSubscribedEvents
     * 
     */
    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::REQUEST => 'logRequestData'
        ];
    }
}
