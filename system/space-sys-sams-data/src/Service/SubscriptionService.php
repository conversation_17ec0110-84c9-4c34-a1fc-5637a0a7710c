<?php

namespace App\Service;

use Exception;
use Symfony\Component\HttpFoundation\Request;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use App\Helper\FileHelper;
use App\Connector\HttpClientWithCacheTimeouter;
use App\Connector\HttpClient;
use Psr\Log\LoggerInterface;


class SubscriptionService
{
    use LoggerTrait;

    /**
     * @var HttpClientWithCacheTimeouter
     */
    private $client;

    /**
     * @var HttpClient
     */
    private $httpClient;

    /**
     * @var array
     */
    private $cacheDurations;

    /**
     * @var array
     */
    private $timeouts;

    /**
     * @var string
     */
    private $certifDir;

    /**
     * @var string
     */
    private $url;

    /**
     * @var array
     */
    private $basicAuthV2;

    /**
     * @var string
     */
    private $urlV2;

    /**
     * @var FileHelper
     */
    private $fileHelper;

    /**
     * @var string
     */
    private $certNameV2;

    /**
     * @var string
     */
    private $cartUrlV2;
    
    /**
     * @param HttpClientWithCacheTimeouter $httpClient
     * @param HttpClient $httpClientSimple
     * @param array $cacheDurations
     * @param array $timeouts
     * @param string $certNameV2
     * @param string $certifDir
     * @param string $authHttpLoginV2
     * @param string $authHttpPasswordV2
     * @param string $urlV2
     * @param string $cartUrlV2
     * @param FileHelper $fileHelper
     */
    public function __construct(
        HttpClientWithCacheTimeouter $httpClient,
        HttpClient $httpClientSimple,
        array $cacheDurations,
        array $timeouts,
        string $certNameV2,
        string $certifDir,
        string $authHttpLoginV2,
        string $authHttpPasswordV2,
        string $urlV2,
        string $cartUrlV2,
        FileHelper $fileHelper
    ) {
        $this->client = $httpClient;
        $this->httpClient = $httpClientSimple;
        $this->cacheDurations = $cacheDurations;
        $this->timeouts = $timeouts;
        $this->certNameV2 = $certNameV2;
        $this->certifDir = $certifDir;
        $this->basicAuthV2 = [$authHttpLoginV2, $authHttpPasswordV2];
        $this->urlV2 = $urlV2;
        $this->fileHelper = $fileHelper;
        $this->cartUrlV2 = $cartUrlV2;

        // Set logger for HttpClient if available
        if (isset($this->logger)) {
            $this->httpClient->setLogger($this->logger);
        }
    }

    /**
     * Return certif file path 
     * @param string $certifName 
     * @param string $extension 
     * @return string
     */
    private function getCertifFile(string $certifName, string $extension): string
    {
        return $this->certifDir . '/' . $this->fileHelper->setExtension($certifName, $extension);
    }

    public function getSubscription(string $userId, string $vin, string $target): WSResponse
    {
        $options =  [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'timeout' => $this->timeouts['product_assessed'],
            'local_cert' => $this->getCertifFile($this->certNameV2, '.pem'),
            'auth_basic' => $this->basicAuthV2,
        ];
        return $this->client->request(
            Request::METHOD_GET,
            "{$this->urlV2}/accounts/{$userId}/vehicles/{$vin}/subscription?target={$target}",
            CacheInfosProvider::getSubscriptionCacheKeyForLastResponse($userId, $vin, $target),
            $this->cacheDurations['product_assessed'] ?? -1,
            $options,
        );
    }

    public function checkoutItem(string $userId, string $brand, string $country, string $target, array $serviceParams): WSResponse
    {
        $options =  [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'timeout' => $this->timeouts['product_assessed'],
            'auth_basic' => $this->basicAuthV2,
            'local_cert' => $this->getCertifFile($this->certNameV2, '.cer'),
            'local_pk' => $this->getCertifFile($this->certNameV2, '.key'),
            'json' => $serviceParams  // Add the request body
        ];

        // Use simple HttpClient for checkout operations (no caching)
        return $this->httpClient->request(
            Request::METHOD_POST,
            "{$this->cartUrlV2}/channel/cart/{$brand}/{$country}/{$target}/{$userId}/items/checkout",
            $options
        );
    }
}