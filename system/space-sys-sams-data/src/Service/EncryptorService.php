<?php

namespace App\Service;

use Exception;
use Psr\Log\LoggerInterface;

class EncryptorService
{
    /**
     * @var string
     */
    private $_protocol = "AES-256-CBC";

    /**
     * @var LoggerInterface
     */
    private $_logger;

    /**
     * @var string
     */
    private $_secret;

    /**
     * @var string
     */
    private $_vector;

    /**
     * @param LoggerInterface $logger
     * @param string $secret
     * @param string $vector
     */
    public function __construct(LoggerInterface $logger, string $secret, string $vector)
    {
        $this->_logger = $logger;
        $this->_secret = $secret;
        $this->_vector = $vector;
    }

    public function encrypt($data)
    {
        $value = openssl_encrypt($data, $this->_protocol, $this->_secret, 0, $this->_vector);
        if ($value === false) {
            $this->_logger->error('Error While Encrypting data');
            throw new Exception('Error While Encrypting data');
        }
        return $value;
    }

    public function decrypt($data)
    {
        $value = openssl_decrypt($data, $this->_protocol, $this->_secret, 0, $this->_vector);
        if ($value === false || !preg_match("//u", $value)) {
            $this->_logger->error('Error While Decrypting data');
            throw new Exception('Error While Decrypting data');
        }
        return $value;
    }
}