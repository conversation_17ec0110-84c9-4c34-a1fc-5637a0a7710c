<?php

namespace App\Service;

use Symfony\Component\Cache\Adapter\AdapterInterface;
use Symfony\Contracts\Cache\CacheInterface;

class CacheAdapter implements CacheInterface
{
    /**
     * @var AdapterInterface
     */
    private $cache;

    public function __construct(AdapterInterface $cache)
    {
        $this->cache = $cache;
    }

    public function get(string $key, callable $callback, float $beta = null, array &$metadata = null): mixed {
        $item = $this->cache->getItem($key);
        if (!$item->isHit()) {
            $result = $callback($item);
            $this->cache->save($item->set($result));
        }
        return $item->get();
    }
    
    public function delete(string $key): bool {
        $item = $this->cache->getItem($key);
        if (!$item->isHit()) { 
            return true;
        }
        return $this->cache->deleteItem($key);
    }

}
