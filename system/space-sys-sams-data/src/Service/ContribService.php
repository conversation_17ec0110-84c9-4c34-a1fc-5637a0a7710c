<?php

namespace App\Service;

use Exception;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;

use App\Model\VehicleInfo;
use App\Helper\WSResponse;
use App\Helper\FileHelper;
use App\Helper\CultureParser;
use App\Model\CatalogModel;
use App\Trait\LoggerTrait;
use App\Connector\HttpClientWithCacheTimeouter;
use App\Helper\BrandProvider;
use App\Helper\ResponseArrayFormat;

class ContribService
{
    use LoggerTrait;

    /**
     * @var HttpClientWithCacheTimeouter
     */
    private $client;

    /**
     * @var array
     */
    private $cacheDurations;

    /**
     * @var array
     */
    private $timeouts;

    /**
     * @var string
     */
    private $certifDir;

    /**
     * @var string
     */
    private $url;

    /**
     * @var string
     */
    private $certName;

    /**
     * @var array
     */
    private $basicAuth;

    /**
     * @var array
     */
    private $basicAuthV2;

    /**
     * @var string
     */
    private $contribUrlV2;

    /**
     * @var string
     */
    private $certNameV2;

    /**
     * @var FileHelper
     */
    private $fileHelper;

    public function __construct(
        HttpClientWithCacheTimeouter $httpClient,
        array $cacheDurations,
        array $timeouts,
        string $certName,
        string $authHttpLogin,
        string $authHttpPassword,
        string $certifDir,
        string $authHttpLoginV2,
        string $authHttpPasswordV2,
        string $contribUrlV2,
        FileHelper $fileHelper,
        string $certNameV2
    ) {
        $this->client = $httpClient;
        $this->cacheDurations = $cacheDurations;
        $this->timeouts = $timeouts;
        $this->basicAuth = [$authHttpLogin, $authHttpPassword];
        $this->certName = $certName;
        $this->certifDir = $certifDir;
        $this->fileHelper = $fileHelper;
        $this->contribUrlV2 = $contribUrlV2;
        $this->basicAuthV2 = [$authHttpLoginV2, $authHttpPasswordV2];
        $this->certNameV2 = $certNameV2;
    }

    public function setUrl(string $url)
    {
        $this->url = $url;
    }

    public function getDefaultContribUrl()
    {
        return ($this->contribUrlV2) ? $this->contribUrlV2 : null;
    }

    private function getCertifFile(string $certifName, string $extension): string
    {
        return $this->certifDir . '/' . $this->fileHelper->setExtension($certifName, $extension);
    }

    public function getBrandCode(string $brand): ?string
    {
        $brand = (in_array($brand, ['OP', 'VX'])) ? 'OV' : $brand;
        if (in_array($brand, ['AP', 'AC', 'DS', 'OV'])) {
            return $brand;
        }
        $brand = BrandProvider::getBrandId($brand);
        return $brand;
    }

    public function getContribDataByProduct(string $brand, string $culture, string $productId, ?string $source = 'APP')
    {
        $samsBrand = $this->getBrandCode($brand);

        $options =  [
            'headers' => [
                'Content-Type' => 'application/json',
                'brand' => $samsBrand,
                'culture' => $culture
            ],
            'timeout' => $this->timeouts['contrib'],
            'local_cert' => $this->getCertifFile($this->certName, '.cer'),
            'local_pk'   => $this->getCertifFile($this->certName, '.key'),
            'auth_basic' => $this->basicAuth,
        ];
        return $this->client->request(
            Request::METHOD_GET,
            $this->url . "/contrib/getInfoByProduct/{$productId}",
            CacheInfosProvider::getContribCacheKeyForLastResponse($brand, $culture, $productId),
            $this->cacheDurations['contrib'] ?? -1,
            $options
        );
    }

    public function getContribDataByProductIds(string $brand, string $culture, string $productIds, ?string $source = 'APP')
    {
        $samsBrand = $this->getBrandCode($brand);

        $options =  [
            'headers' => [
                'Content-Type' => 'application/json',
                'brand' => $samsBrand,
                'culture' => $culture
            ],
            'timeout' => $this->timeouts['contrib'],
            'local_cert' => $this->getCertifFile($this->certNameV2, '.pem'),
            'auth_basic' => $this->basicAuthV2,
        ];
        return $this->client->request(
            Request::METHOD_GET,
            $this->url . "/v2/contrib/getInfoByProduct?productIds={$productIds}",
            CacheInfosProvider::getContribCacheKeyForLastResponse($brand, $culture, $productIds),
            $this->cacheDurations['contrib'] ?? -1,
            $options
        );
    }
}
