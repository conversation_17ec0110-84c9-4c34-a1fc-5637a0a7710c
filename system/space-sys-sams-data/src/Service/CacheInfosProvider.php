<?php

namespace App\Service;

class CacheInfosProvider {

    public static function getCatalogCacheKeyForLastResponse(string $brand, string $culture, string $vin): string
    {
        return "api_sys_sams_catalog_{$vin}_{$culture}_last";
    }

    public static function getContribCacheKeyForLastResponse(string $brand, string  $culture, string $marketingProductSheetId)
    {
        return "api_sys_sams_contrib_{$brand}_{$culture}_{$marketingProductSheetId}_last";
    }

    public static function getSubscriptionCacheKeyForLastResponse(string $userId, string $vin, string $target)
    {
        return "api_sys_sams_contrib_{$userId}_{$vin}_{$target}_last";
    }
}