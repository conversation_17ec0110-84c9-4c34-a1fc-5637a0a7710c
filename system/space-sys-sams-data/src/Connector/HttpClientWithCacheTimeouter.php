<?php

namespace App\Connector;

use Exception;
use Psr\Cache\CacheItemPoolInterface;
use S<PERSON>fony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

use App\Helper\WSResponse;
use App\Message\AsynchronousWSCallMessage;
use App\Trait\LoggerTrait;

/**
 * Kind of HttpClientInterface decorator to support queue messaging
 */
class HttpClientWithCacheTimeouter implements HttpClientWithCacheTimeouterInterface
{
    use LoggerTrait;
    /**
     * @var HttpClientInterface
     */
    private $client;

    /**
     * @var CacheItemPoolInterface
     */
    private $cache;

    /**
     * @var MessageBusInterface
     */
    private $bus;

    /**
     * @param HttpClientInterface $client
     * @param CacheItemPoolInterface $cache
     * @param MessageBusInterface $bus
     */
    public function __construct(HttpClientInterface $client, CacheItemPoolInterface $cache, MessageBusInterface $bus)
    {
        $this->client = $client;
        $this->cache  = $cache;
        $this->bus = $bus;
    }

    public function request(string $method, string $url, string $cacheKey, int $duration, array $options = [], $recall = true): WSResponse
    {
        try {
            $this->logger->info("Call URL: {$url}");
            $response = $this->client->request($method, $url, $options);
            $statusCode = $response->getStatusCode();
            $content = $response->getContent(false);
            $requestInfo = $response->getInfo();
            $response = new WSResponse($statusCode, $content);
            $this->logger->debug("**PERF** : $url  ".$requestInfo['total_time']);
            $this->eraseOldCacheAndSetNew($cacheKey, $duration, $response);
            return $response;
        } catch (TransportExceptionInterface $e) {
            $this->logger->error("Error: Call URL: {$url} ({$e->getMessage()}");
            if ($recall) {
                $this->logger->info("Call URL: {$url} ({$e->getMessage()}), trying to fetch old response");
                $item = $this->cache->getItem($cacheKey);
                $this->bus->dispatch(new AsynchronousWSCallMessage($url, $options, $method, $cacheKey, $duration));
                if ($item->isHit()) {
                    $this->logger->info("Call URL: {$url} , Old data successfully fetched");
                    return $item->get();
                }
            }
            throw new Exception($e->getMessage());
        }
    }

    /**
     *Erase old data cache with the new one
     * @param string $cacheKey
     * @param integer $duration
     * @param WSResponse $value
     * @return void
     */
    private function eraseOldCacheAndSetNew(string $cacheKey, int $duration, WSResponse $value) {
        $this->cache->deleteItem($cacheKey);
        $item = $this->cache->getItem($cacheKey);
        $item->set($value);
        $item->expiresAfter($duration);
        $this->cache->save($item);
    }
}
