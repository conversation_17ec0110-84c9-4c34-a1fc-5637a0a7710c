<?php

namespace App\Connector;

use App\Helper\WSResponse;
use Symfony\Contracts\HttpClient\ResponseInterface;

interface HttpClientWithCacheTimeouterInterface {
    
    /**
     * Requests an HTTP resource.
     *
     * Responses MUST be lazy, but their status code MUST be
     * checked even if none of their public methods are called.
     *
     * Implementations are not required to support all options described above; they can also
     * support more custom options; but in any case, they MUST throw a TransportExceptionInterface
     * when an unsupported option is passed.
     *
     * @throws TransportExceptionInterface When an unsupported option is passed
     */
    public function request(string $method, string $url, string $cacheKey, int $duration, array $options = []): WSResponse;
}