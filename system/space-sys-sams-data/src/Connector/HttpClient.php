<?php

namespace App\Connector;

use Exception;
use Symfony\Contracts\HttpClient\HttpClientInterface;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;

/**
 * HttpClientInterface decorator 
 */
class HttpClient 
{
    use LoggerTrait;
    /**
     * @var HttpClientInterface
     */
    private $client;

    /**
     * @param HttpClientInterface $client
     */
    public function __construct(HttpClientInterface $client)
    {
        $this->client = $client;
    }

    /**
     * Send request
     * @param string $url 
     * @param string $method 
     * @param array $options 
     * @return WSResponse
     */
    public function request(string $method, string $url, array $options = []): WSResponse
     {
        $code = null;
        try {
            $this->logger->info("start call $url");
            $response = $this->client->request($method, $url, $options);
            $code = $response->getStatusCode();
            $requestInfo = $response->getInfo();

            $content = $response->getContent(false);
            $this->logger->debug("**PERF** : end call $url  ".$requestInfo['total_time']);

        } catch (Exception $e) {

            $content = $e->getMessage();
            $code  = $code ?? $e->getCode();
            $this->logger->error("HttpClient: failed request $url => $code : $content");
        }
        
        return new WSResponse($code, $content);
    }
}
