<?php

namespace App\Message;

/**
 * Represents an asynchronous WS Call Model 
 */
class AsynchronousWSCallMessage
{
    /**
     * @var string
     */
    private $url;

    /**
     * @var array
     */
    private $options;

    /**
     * @var string
     */
    private $method;

    /**
     * @var string
     */
    private $cacheKey;

    /**
     * @var int
     */
    private $cacheDuration;

    const EXTRA_TIMEOUT = 20;

    /**
     * @param string $url
     * @param array $options
     * @param string $method
     * @param string $cacheKey
     * @param integer $cacheDuration
     */
    public function __construct(string $url, array $options, string $method, string $cacheKey, int $cacheDuration)
    {
        $this->url = $url;
        $this->options = $options;
        $this->method = $method;
        $this->cacheKey = $cacheKey;
        $this->cacheDuration = $cacheDuration;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function getOptions(): array
    {
        $this->options['timeout'] = intval($this->options['timeout']) + self::EXTRA_TIMEOUT; 
        return $this->options;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function getCacheKey(): string
    {
        return $this->cacheKey;
    }

    public function getCacheDuration(): int
    {
        return $this->cacheDuration;
    }
}
