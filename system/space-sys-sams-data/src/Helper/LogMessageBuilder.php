<?php

namespace App\Helper;

use App\EventSubscriber\LogRequestSubscriber;
use App\Service\EncryptorService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class LogMessageBuilder
{
    const SEPARATOR = "\t";

    /**
     * @var EncryptorService
     */
    private $_encryptor;

    public function __construct(EncryptorService $encryptorService)
    {
        $this->_encryptor = $encryptorService;
    }

    public function buildRequestTextLog(Request $request)
    {
        return $this->_getNonEncryptedPart($request) . self::SEPARATOR . $this->_getRequestLog($request);
    }

    public function buildResponsetTextLog(Request $request, Response $response)
    {
        return $this->_getNonEncryptedPart($request, "RESPONSE") . self::SEPARATOR . $this->_getResponseLog($request, $response);
    }

    private function _getRequestLog(Request $request)
    {
        $executionDate = date('Y-m-d H:i:s');
        $requestParameters = json_encode($request->query->all());
        $headers = json_encode($request->headers->all());
        $requestBody = $request->request->all() ? json_encode($request->request->all()) : "";
        $nonEncryptedRequestLog = "REQUEST  LOG" . " - Time : $executionDate" . " - URL : {$request->getRequestUri()}" . " - PARAMETERS : " . $requestParameters
            . " - HEADERS : " . $headers
            . ($requestBody ? ' - BODY : ' . $requestBody : "");
        return $this->_encryptor->encrypt($nonEncryptedRequestLog);
    }

    private function _getResponseLog(Request $request, Response $response)
    {
        $executionDate = date('Y-m-d H:i:s');
        $responseContent = $response->getContent();
        $nonEncryptedResponseLog = "RESPONSE LOG" . " - Time : $executionDate" . " - URL : {$request->getRequestUri()}" . " - RESPONSE : " . $responseContent;
        return $this->_encryptor->encrypt($nonEncryptedResponseLog);
    }

    private function _getNonEncryptedPart(Request $request, $type = "REQUEST")
    {
        $nonEncrypted = [
            $type,
            gethostname(),
            $request->headers->get('account_id', ''),
            $request->headers->get('User-Agent'),
            $request->headers->get(LogRequestSubscriber::QUERY_ID),
            $request->getRequestUri()
        ];
        return implode(self::SEPARATOR, $nonEncrypted);
    }
}
