<?php

namespace App\Helper;

use Symfony\Component\HttpFoundation\Response;

/**
 * Manage rest responses.
 */
class WSResponse implements ResponseArrayFormat
{
    private int $code = Response::HTTP_OK;

    private mixed $data;

    public function __construct(int $code, mixed $data)
    {
        $this->code = $code;
        $this->data = $data;
    }

    public function setCode(int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function setData(mixed $data): self
    {
        $this->data = $data;

        return $this;
    }

    public function getData(): mixed
    {
        return $this->data;
    }

    public function toArray(): array
    {
        try {
            return json_decode($this->data, true);
        } catch (\Exception $e) {
            return [
                'errors' => [
                    'message' => $e->getMessage()
                ]
            ];
        }
        
    }
}
