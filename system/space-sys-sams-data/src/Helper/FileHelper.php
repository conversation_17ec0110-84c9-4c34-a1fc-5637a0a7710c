<?php

namespace App\Helper;

/**
 * This helper is used to verify if a certification filename has the extension part if not it is added but can be exploited in other use cases
 */
class FileHelper {

	/**
	 * This helper verifies if a filename has the extension part if not it is added
	 * @param string $fileName 
	 * @param string $extension (example: .cer)
	 * @return string
	 */
    public function setExtension(string $fileName, string $extension):string 
    {
        if (substr($fileName, -strlen($extension)) == $extension) {
            return $fileName;
        }

        return $fileName.$extension;
    }
}
