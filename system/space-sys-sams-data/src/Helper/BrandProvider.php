<?php

namespace App\Helper;

/**
 * Brand provider
 */
class BrandProvider {

    private const BRANDS = [
        'AL' => '83', // ALFA ROMEO
        'AP' => '31', // PEUGEOT
        'AC' => '30', // CITROEN
        'DS' => '33', // DS
        'OP' => '43', // OPEL
        'VX' => '', // VAUXHALL
        'FT' => '00', // FIAT
        'FO' => '77', // FIAT PROFESSIONAL
        'AH' => '66', // ABARTH
        'AR' => '83', // 
        'CY' => '92', // CHRYSLER
        'DG' => '56', // DODGE
        'JE' => '57', // JEEP
        'LA' => '70', // LANCIA
        'RM' => '58', // RAM
        'MA' => '98' // 
    ];
    
    public static function getBrandId(string $brand) {
        if(!empty(self::BRANDS[$brand])) {

            return self::BRANDS[$brand];
        }

        return null;
    }
}
