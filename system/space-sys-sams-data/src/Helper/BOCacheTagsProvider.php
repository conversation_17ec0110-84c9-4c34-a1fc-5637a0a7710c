<?php

namespace App\Helper;

/**
 * BO Cache Tags Helps to construct BO tag names from available data
 */
class BOCacheTagsProvider
{
    const PREFIX = "BO_SETTINGS";

    /**
     * return tags to invalidate depending on available data
     * @param ?string $brand 
     * @param ?string $country 
     * @param ?string $source 
     * @param ?string $language 
     * @return array
     */
    public static function getTagList(?string $brand = null, ?string $country = null, ?string $source = null, ?string $language = null)
    {
        $tags = [];
        $tags[] = $tag = self::PREFIX;

        if ($brand) {
           $tags[] = $tag .='_'.strtoupper($brand);
        }

        if ($country) {
           $tags[] = $tag.='_'.strtoupper($country);
        }

        if ($source) {
           $tags[] = $tag.='_'.strtoupper($source);
        }

        if ($language) {
            $tags[] = $tag.='_'.strtoupper($language);
        }

        return $tags;
    }
}
