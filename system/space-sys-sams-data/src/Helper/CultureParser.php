<?php

namespace App\Helper;

/**
 * Parse culture parameter
 */
class CultureParser {
    /**
     * splite cultur into country and language
     * @param string $culture 
     * @return array [$country, $language]
     */
    public static function getCountryAndLanguage( string $culture) {
        $country = $language = '';

        $data = explode('_', $culture);
        if (count($data) >= 2) {
            $language = strtolower($data[0]);
            $country = strtoupper($data[1]);
        }
        if (! $language) {
            $data = explode('-', $culture);
            if (count($data) >= 2) {
                $language = strtolower($data[0]);
                $country = strtoupper($data[1]);
            }
        }
        
        return [
            'country' => $country, 
            'language' => $language
        ];
    }
}
