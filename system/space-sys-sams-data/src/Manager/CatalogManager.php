<?php

namespace App\Manager;

use Exception;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;

use App\Trait\ValidationResponseTrait;
use App\Helper\BrandProvider;
use App\Helper\BundlesTransformer;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Helper\CultureParser;
use App\Helper\UrlFormater;
use App\Model\CatalogModel;
use App\Trait\LoggerTrait;
use App\Service\CatalogService;
use App\Manager\ContribManager;

class CatalogManager
{
    use LoggerTrait;
    use ValidationResponseTrait;

    /**
     * @var ValidatorInterface
     */
    private $validator;

    /**
     * @var CatalogService
     */
    private $service;

    /**
     * @var string[]
     */
    private $domtoms;

    public function __construct(CatalogService $service, ?array $domtoms = [], ValidatorInterface $validator)
    {
        $this->validator = $validator;
        $this->service = $service;
        $this->domtoms = $domtoms;
    }

    public function getBrandCode(string $brand): string
    {
        $brand = (in_array($brand, ['OP', 'VX'])) ? 'OV' : $brand;
        if (in_array($brand, ['AP', 'AC', 'DS', 'OV'])) {
            return $brand;
        }
        $brand = BrandProvider::getBrandId($brand);
        return $brand;
    }

    public function getAssessedProducts(array $params): ResponseArrayFormat
    {
        try {
            $brand = $params['brand'];
            $brandId = $this->getBrandCode($brand);
            $vin = $params['vin'];
            $culture = $params['language'] . '-' . $params['country'];
            $source = "WEB";
            $vehicleInfo = [
                "store" => [
                    "origin" => $brandId,
                    "location" => $params['country'],
                    "audience" => "B2C",
                    "channel" => "WEB",
                    "brand" => $brandId,
                    "country" => $params['country']
                ],
                "vehicle" => $vin,
                "customer" => $params['userId']
            ];
            $catalogResponse = $this->service->getAssessedProducts($vehicleInfo);
            if ($catalogResponse->getCode() !== Response::HTTP_OK) {
                $responseData = $catalogResponse->getData();
                $responseData = json_decode($responseData);
                if (is_object($responseData) && $responseData instanceof \stdClass && isset($responseData->message))
                {
                    $message = isset($responseData->message) ? $responseData->message : $responseData;
                    $this->logger->error('=> '.__METHOD__.' => error : '.$message . 'for vin : {$vin}');
                    return new ErrorResponse($message, $catalogResponse->getCode());
                }
                $message = (isset(json_decode($responseData->error)->message)) ? json_decode($responseData->error)->message : ((isset(json_decode($responseData->error)->title)) ? json_decode($responseData->error)->title : $responseData);
                $this->logger->error('=> '.__METHOD__.' => error : '.$message . 'for vin : {$vin}');
                return new ErrorResponse($message, $catalogResponse->getCode());
            }
            $catalogList = $catalogResponse->getData();
            $catalogList = json_decode($catalogList)->data;
            if (!is_array($catalogList) and isset($catalogList->statusCode) and $catalogList->statusCode !== 200) {
                $catalogList = json_decode($catalogList->body, true);
                $this->logger->error('=> '.__METHOD__.' => error : '.$catalogList . 'for vin : {$vin}');
                $message = (isset($catalogList['message'])) ? $catalogList['message'] : $catalogList;
                return new ErrorResponse($message, $catalogList['statusCode']);
            }
            return new SuccessResponse($catalogList);
        } catch (Exception $e) {
            $this->logger->error("Error: [function getCatalog] Call SAMS catalog for vin {$vin} ({$e->getMessage()})");
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}
