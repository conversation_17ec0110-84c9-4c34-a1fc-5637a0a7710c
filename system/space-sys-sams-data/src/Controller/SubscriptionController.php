<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Model;

use App\Trait\ValidationResponseTrait;
use App\Helper\ErrorResponse;
use App\Validator\VinValidator;
use App\Validator\BrandValidator;
use App\Manager\SubscriptionManager;

#[Route('/v1/subscription', name: 'Subscription_')]
class SubscriptionController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('', name: 'getSubscription', methods: ['GET'])]
    #[OA\Tag(name: 'SUBSCRIPTION API')]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'target',
        in: 'query',
        description: 'target parameter',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', type: 'object')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Unporcessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function getSubscription(Request $request, SubscriptionManager $subscriptionManager, ValidatorInterface $validator): JsonResponse
    {
        $userId = $request->headers->get('userId') ?? '';
        $vin = $request->headers->get('vin') ?? '';
        $target = $request->query->get('target') ?? '<string>';
        $errors = $validator->validate(
            compact('vin', 'userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'vin' => VinValidator::getConstraints()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $messages = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($messages['content'], $messages['code']);
        }
        $response = $subscriptionManager->getSubscription($userId, $vin, $target)->toArray();
        return $this->json($response['content'], $response['code']);
    }

    #[Route('/items/checkout', name: 'itemCheckout', methods: ['POST'])]
    #[OA\Tag(name: 'Checkout API')]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'target',
        in: 'header',
        description: 'target parameter',
        schema: new OA\Schema(
            type: 'string',
            enum: ['B2B', 'B2C']
        ),
        required: true
    )]
    #[OA\Parameter(
        name: 'source',
        in: 'header',
        description: 'target parameter',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'header',
        description: 'target parameter',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'header',
        description: 'target parameter',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'header',
        description: 'target parameter',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\RequestBody(
        required: true,
        content: new JsonContent(
            type: 'object',
            required: ['vehicleDescription', 'items'],
            properties: [
                new OA\Property(
                    property: 'vehicleDescription', 
                    type: 'string',
                    description: 'Description of the vehicle',
                    example: 'DS3 xxxxx'
                ),
                new OA\Property(
                    property: 'items',
                    type: 'array',
                    description: 'List of subscription items',
                    items: new OA\Items(
                        type: 'object',
                        required: ['type', 'id', 'tncVersion', 'tncId', 'tncUrl'],
                        properties: [
                            new OA\Property(property: 'type', type: 'string', example: 'zuoraRatePlan'),
                            new OA\Property(property: 'id', type: 'string', example: '8adc8f99697a22240169916559841311'),
                            new OA\Property(property: 'tncVersion', type: 'string', example: '1.4'),
                            new OA\Property(property: 'tncId', type: 'string', example: '162'),
                            new OA\Property(
                                property: 'tncUrl', 
                                type: 'string', 
                                format: 'url',
                                example: 'https://connect.opel.de/sites/ov/files/2024-08/DE_TC_Connect%20Plus_06_2024_7.pdf'
                            )
                        ]
                    )
                )
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'eShopOrderId'),
                ])
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Unporcessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function checkoutItem(Request $request, SubscriptionManager $subscriptionManager, ValidatorInterface $validator): JsonResponse
    {
        $userId = $request->headers->get('userId') ?? '';
        $vin = $request->headers->get('vin') ?? '';
        $target = $request->headers->get('target') ?? '';
        $brand = $request->headers->get('brand') ?? '';
        $source = $request->headers->get('source') ?? '';
        $language = $request->headers->get('language') ?? '';
        $country = $request->headers->get('country') ?? '';
        $content = json_decode($request->getContent(), true);
        $errors = $validator->validate(
            compact('vin', 'userId','target','brand','source','language','country'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'vin' => VinValidator::getConstraints(),
                'target' => new Assert\NotBlank(),
                'source' => new Assert\NotBlank(),
                'country' => new Assert\NotBlank(),
                'language' => new Assert\NotBlank(),
                'brand' => BrandValidator::getConstraintsForAll()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $messages = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($messages['content'], $messages['code']);
        }
        $response = $subscriptionManager->checkoutItem($userId, $vin, $target, $brand, $source, $language, $country, $content)->toArray();
        return $this->json($response['content'], $response['code']);
    }
}