<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Model;

use App\Trait\ValidationResponseTrait;
use App\Helper\ErrorResponse;
use App\Validator\BrandValidator;
use App\Validator\Culture;
use App\Validator\VinValidator;
use App\Manager\CatalogManager;

#[Route('/v1/catalog', name: 'catalog_')]
class CatalogController extends AbstractController
{
    use ValidationResponseTrait;
    #[Route('', methods: ['GET'])]
    #[OA\Tag(name: 'SAMS V1 : Catalog')]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        description: 'Language',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'message'),
                ])
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Unporcessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function getCatalog(Request $request, CatalogManager $catalogManager, ValidatorInterface $validator): JsonResponse
     {
        $userId = $request->headers->get('userId') ?? '';
        $vin = $request->headers->get('vin') ?? '';
        $brand = $request->query->get('brand') ?? '';
        $country = $request->query->get('country') ?? '';
        $language = $request->query->get('language') ?? '';
        $params = compact('userId', 'vin',  'brand',  'country',  'language');

        $errors = $validator->validate(
            compact('vin', 'brand'),
            new Assert\Collection([
                'brand'    => BrandValidator::getConstraintsForAll(),
                'vin' => VinValidator::getConstraints()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $messages = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($messages['content'], $messages['code']);
        }

        $response = $catalogManager->getAssessedProducts($params)->toArray();
        
        return $this->json($response['content'], $response['code']);
    }

}
