<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraints as Assert;

class BrandValidator
{
    /**
     * Get all the brands.
     *
     * @return array
     */
    public static function all(): array
    {
        return ['AL', 'AP', 'AC', 'DS', 'OP', 'VX', 'FT', 'FO', 'AH', 'AR', 'CY', 'DG', 'JE', 'LA', 'RM', 'MA'];
    }

    /**
     * Get all brand IDs.
     *
     * @return array
     */
    public static function allBrandIds(): array
    {
        return ['83', '31', '30', '33', '43', '00', '66', '55', '56', '57', '70', '58'];
    }

    /**
     * Get brand constraints.
     *
     * @return array
     */
    public static function getConstraintsForAll(): array
    {
        return [
            new Assert\NotBlank,
            new Assert\Choice(BrandValidator::all()),
        ];
    }

    /**
     * Get brand ID constraints
     *
     * @return array
     */
    public static function getConstraintsForBrandId(): array
    {
        return [
            new Assert\NotBlank,
            new Assert\Choice(BrandValidator::allBrandIds()),
        ];
    }

    /**
     * Get constraints for either brand code or ID
     *
     * @return array
     */
    public static function getConstraintsForBrandCodeOrId(): array
    {
        return [
            new Assert\NotBlank,
            new Assert\Choice([
                'choices' => array_merge(BrandValidator::all(), BrandValidator::allBrandIds()),
                'message' => 'Invalid brand code or ID'
            ]),
        ];
    }
}
