<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints\Country;
use Symfony\Component\Validator\Constraints\Language;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Validation;

class CultureValidator extends ConstraintValidator
{
    public function validate($value, Constraint $constraint)
    {
        /* @var $constraint \App\Validator\Culture */
        $value = trim($value);
        if (strlen($value) != 5) {
            $this->context->buildViolation("Invalid culture value (xx-XX)")
                ->addViolation();
            return;
        }
        $country  = strtoupper(substr($value, 3));
        $language = strtolower(substr($value, 0, 2));
        $this->validateCountry($country);
        $this->validateLanguage($language);
    }

    /**
     * @param string $country
     * @return void
     */
    private function validateCountry($value)
    {
        $validation = Validation::createValidator();
        $violations = $validation->validate($value, [
            new Country(['message' => 'Invalid Country value'])
        ]);
        if (0 !== count($violations)) {
            // there are errors, now you can show them
            foreach ($violations as $violation) {
                $this->context->buildViolation($violation->getMessage())
                    ->addViolation();
                continue;
            }
        }
    }

    private function validateLanguage($value)
    {
        $validation = Validation::createValidator();
        $violations = $validation->validate($value, [
            new Language(['message' => 'Invalid Language value'])
        ]);
        if (0 !== count($violations)) {
            // there are errors, now you can show them
            foreach ($violations as $violation) {
                $this->context->buildViolation($violation->getMessage())
                    ->addViolation();
                continue;
            }
        }
    }
}
