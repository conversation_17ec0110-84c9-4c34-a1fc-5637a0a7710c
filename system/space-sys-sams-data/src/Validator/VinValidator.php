<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraints as Assert;

class VinValidator
{
    /**
     * Get Country constraints.
     *
     * @return array
     */
    public static function getConstraints(): array
    {
        return [
            new Assert\NotBlank,
            new Assert\Type(['type' => 'alnum']),
            new Assert\Length(['min' => 17, 'max' => 17])
        ];
    }
}


