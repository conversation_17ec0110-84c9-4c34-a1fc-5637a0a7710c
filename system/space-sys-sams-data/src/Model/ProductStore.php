<?php

namespace App\Model;

use <PERSON><PERSON>\Serializer\Annotation\Expose;
use <PERSON><PERSON>\Serializer\Annotation\SerializedName;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * Assessed Product Store Model
 */
class ProductStore
{
	/**
     * @var string
     * @Expose
     * @Type("string")
     * @SerializedName("origin")
     */
	private $origin;

	/**
     * @var string
     * @Expose
     * @Type("string")
     * @SerializedName("location")
     */
	private $location;

	/**
     * @var string
     * @Expose
     * @Type("string")
     * @SerializedName("source")
     */
	private $channel;

	/**
     * @var string
     * @Expose
     * @Type("string")
     * @SerializedName("audience")
     */
	private $audience;


    /**
     * @return string
     */
    public function getOrigin()
    {
        return $this->origin;
    }

    /**
     * @param string $origin
     *
     * @return self
     */
    public function setOrigin($origin)
    {
        $this->origin = $origin;

        return $this;
    }

    /**
     * @return string
     */
    public function getLocation()
    {
        return $this->location;
    }

    /**
     * @param string $location
     *
     * @return self
     */
    public function setLocation($location)
    {
        $this->location = $location;

        return $this;
    }

    /**
     * @return string
     */
    public function getChannel()
    {
        return $this->channel;
    }

    /**
     * @param string $channel
     *
     * @return self
     */
    public function setChannel($channel)
    {
        $this->channel = $channel;

        return $this;
    }

    /**
     * @return string
     */
    public function getAudience()
    {
        return $this->audience;
    }

    /**
     * @param string $audience
     *
     * @return self
     */
    public function setAudience($audience)
    {
        $this->audience = $audience;

        return $this;
    }
}