<?php

namespace App\Model;

use App\Model\ProductStore;
use <PERSON><PERSON>\Serializer\Annotation\Expose;
use <PERSON><PERSON>\Serializer\Annotation\SerializedName;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * Vehicle Info Model
 */
class VehicleInfo
{
	/**
     * @var string
     * @Expose
     * @Type("string")
     * @SerializedName("vehicle")
     */
	private $vehicle;
    
    /**
     * @var string
     * @Expose
     * @Type("string")
     * @SerializedName("customer")
     */
	private $customer;

	 /**
     * @var string
     * @Expose
     * @Type("string")
     * @SerializedName("culture")
     */
	private $culture;

     /**
     * @var string
     * @Expose
     * @Type("string")
     * @SerializedName("brand")
     */
    private $brand;

	/**
     * @var ProductStore
     * @Expose
     * @Type("App\Model\ProductStore")
     * @SerializedName("store")
     */
	private $store;
                                
    /**
     * @return string
     */
    public function getVehicle()
    {
        return $this->vehicle;
    }

    /**
     * @param string $vehicle
     *
     * @return self
     */
    public function setVehicle($vehicle)
    {
        $this->vehicle = $vehicle;

        return $this;
    }

    /**
     * @return string
     */
    public function getCustomer()
    {
        return $this->customer;
    }

    /**
     * @param string $customer
     *
     * @return self
     */
    public function setCustomer($customer)
    {
        $this->customer = $customer;

        return $this;
    }

    /**
     * @return string
     */
    public function getCulture()
    {
        return $this->culture;
    }

    /**
     * @param string $culture
     *
     * @return self
     */
    public function setCulture($culture)
    {
        $this->culture = $culture;

        return $this;
    }

    /**
     * @param string $brand
     *
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;

        return $this;
    }

    /**
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }
    
    /**
     * @return ProductStore
     */
    public function getStore()
    {
        return $this->store;
    }

    /**
     * @param ProductStore $store
     *
     * @return self
     */
    public function setStore(ProductStore $store)
    {
        $this->store = $store;

        return $this;
    }
}