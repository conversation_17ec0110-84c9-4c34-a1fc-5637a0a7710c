<?php

namespace App\Log;

use Psr\Log\LoggerInterface;

class ApiLogger
{
    /**
     * @var LoggerInterface
     */
    private $_logger;

    public function __construct(LoggerInterface $apiLogger)
    {
        $this->_logger = $apiLogger;
    }

    /**
     * Logs with an arbitrary level.
     *
     * @param mixed   $level
     * @param string  $message
     * @param mixed[] $context
     *
     * @return void
     *
     * @throws \Psr\Log\InvalidArgumentException
     */
    public function log($level, $message, array $context = array())
    {
        $this->_logger->log($level, $message, $context);
    }
}
