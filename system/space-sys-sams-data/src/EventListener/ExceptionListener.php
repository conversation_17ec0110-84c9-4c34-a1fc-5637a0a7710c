<?php

namespace App\EventListener;

use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class ExceptionListener
 */
class ExceptionListener
{

    /**
     * Manage onKernelException
     * @param ExceptionEvent $event
     */
    public function onKernelException(ExceptionEvent $event)
    {
        $code = $event->getThrowable()->getCode();
        $response = new JsonResponse([
            'errors'    => [
                $code => $event->getThrowable()->getMessage()
            ]
        ]);
        // setup the Response object based on the caught exception
        $event->setResponse($response);
    }
}
