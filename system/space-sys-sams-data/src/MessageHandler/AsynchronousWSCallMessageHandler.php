<?php

namespace App\MessageHandler;

use Exception;
use <PERSON>ymfony\Component\Messenger\Attribute\AsMessageHandler;

use App\Message\AsynchronousWSCallMessage;
use App\Connector\HttpClientWithCacheTimeouter;
use App\Trait\LoggerTrait;

#[AsMessageHandler]
class AsynchronousWSCallMessageHandler
{
    use LoggerTrait;
    /**
     * @var HttpClientWithCacheTimeouter
     */
    private $client;

    /**
     * @param HttpClientWithCacheTimeouter $client
     */
    public function __construct(HttpClientWithCacheTimeouter $client)
    {
        $this->client = $client;
    }

    /**
     * @param AsynchronousWSCallMessage $message
     */
    public function __invoke(AsynchronousWSCallMessage $message)
    {
        $this->logger->info("Asynchronous Call URL: {$message->getUrl()} and method {$message->getMethod()} using recall method");
        try {
            $this->client->request(
                $message->getMethod(),
                $message->getUrl(),
                $message->getCacheKey(),
                $message->getCacheDuration(),
                $message->getOptions(),
                false
            );
        } catch (Exception $e) {
            // No retry
            $this->logger->error("Asynchronous Call URL: {$message->getUrl()} and method {$message->getMethod()} ({$e->getMessage()}");
        }
    }
}
