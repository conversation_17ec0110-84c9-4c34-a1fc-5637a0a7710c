<?php
namespace App\MessageHandler;

use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Contracts\Cache\TagAwareCacheInterface;

use App\Message\CacheNotification;

#[AsMessageHandler]
class CacheNotificationHandler
{
    /**
     * @var TagAwareCacheInterface
     */
    private $cache;

    /**
     * @var LoggerInterface
     */
    public $logger;

	public function __construct(TagAwareCacheInterface $cache, LoggerInterface $logger)
    {
        $this->cache = $cache;
    	$this->logger = $logger;
    }

    /**
     * Invalidate cache having the same tags as $cacheNotif tags
     * @param CacheNotification $cacheNotif 
     * @return void
     */
    public function __invoke(CacheNotification $cacheNotif)
    {
        $tags = is_array($cacheNotif->getTags()) ? $cacheNotif->getTags() : [];
        $this->logger->info('invalidate cache tags via CacheNotification Handler: tags :'.json_encode($tags));
        $this->cache->invalidateTags($tags); 
    }
}
